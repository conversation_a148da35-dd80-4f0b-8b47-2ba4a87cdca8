import { ContentFetcher } from '../core/content-fetcher.js';
import { ProxyManager } from '../core/proxy-manager.js';
import { AuthManager } from '../core/auth-manager.js';
import { CacheManager } from '../core/cache-manager.js';
import { ContentProcessor } from '../core/content-processor.js';
import type { ServerConfig, FetchOptions } from '../types/index.js';
import { FetchOptionsSchema, AuthConfigSchema, ProxyConfigSchema } from '../types/index.js';
import { getLogger } from '../utils/logger.js';

interface MCPRequest {
  jsonrpc: string;
  id: string | number;
  method: string;
  params?: any;
}

interface MCPResponse {
  jsonrpc: string;
  id: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

export class MCPServer {
  private contentFetcher: ContentFetcher;
  private proxyManager: ProxyManager;
  private authManager: AuthManager;
  private cacheManager: CacheManager;
  private contentProcessor: ContentProcessor;
  private logger = getLogger({ component: 'MCPServer' });

  constructor(config: ServerConfig) {
    // 初始化核心组件
    this.proxyManager = new ProxyManager(config.proxy);
    this.authManager = new AuthManager(config.security);
    this.cacheManager = new CacheManager(config.cache);
    this.contentProcessor = new ContentProcessor();
    this.contentFetcher = new ContentFetcher(
      this.proxyManager,
      this.authManager,
      this.cacheManager,
      this.contentProcessor,
      {
        userAgents: config.fetcher.userAgents,
        defaultHeaders: config.fetcher.defaultHeaders,
      }
    );
  }

  async start(): Promise<void> {
    this.logger.info('Starting MCP Content Hunter Server');

    // 监听stdin输入
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', async (data: string) => {
      const lines = data.trim().split('\n');
      for (const line of lines) {
        if (line.trim()) {
          await this.handleRequest(line.trim());
        }
      }
    });

    // 发送初始化响应
    this.sendResponse({
      jsonrpc: '2.0',
      id: 'init',
      result: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        serverInfo: {
          name: 'mcp-content-hunter',
          version: '1.0.0',
        },
      },
    });

    this.logger.info('MCP Server started and listening on stdin');
  }

  private async handleRequest(requestStr: string): Promise<void> {
    try {
      const request: MCPRequest = JSON.parse(requestStr);
      
      switch (request.method) {
        case 'tools/list':
          await this.handleListTools(request);
          break;
        case 'tools/call':
          await this.handleToolCall(request);
          break;
        case 'initialize':
          await this.handleInitialize(request);
          break;
        default:
          this.sendError(request.id, -32601, `Method not found: ${request.method}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle request', error as Error, { requestStr });
      this.sendError('unknown', -32700, 'Parse error');
    }
  }

  private async handleInitialize(request: MCPRequest): Promise<void> {
    this.sendResponse({
      jsonrpc: '2.0',
      id: request.id,
      result: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        serverInfo: {
          name: 'mcp-content-hunter',
          version: '1.0.0',
        },
      },
    });
  }

  private async handleListTools(request: MCPRequest): Promise<void> {
    const tools = [
      {
        name: 'fetch_content',
        description: '从指定URL获取并结构化网页内容',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              format: 'uri',
              description: '要抓取的网页URL',
            },
            options: {
              type: 'object',
              properties: {
                useProxy: {
                  type: 'boolean',
                  description: '是否使用代理',
                  default: false,
                },
                renderMode: {
                  type: 'string',
                  enum: ['static', 'dynamic', 'auto'],
                  description: '渲染模式：static(静态), dynamic(动态), auto(自动)',
                  default: 'auto',
                },
                timeout: {
                  type: 'number',
                  description: '请求超时时间(毫秒)',
                  default: 30000,
                },
              },
            },
          },
          required: ['url'],
        },
      },
      {
        name: 'batch_fetch_content',
        description: '批量获取多个URL的结构化内容',
        inputSchema: {
          type: 'object',
          properties: {
            urls: {
              type: 'array',
              items: { type: 'string', format: 'uri' },
              description: '要抓取的URL列表',
            },
            options: {
              type: 'object',
              properties: {
                useProxy: { type: 'boolean', default: false },
                renderMode: { 
                  type: 'string', 
                  enum: ['static', 'dynamic', 'auto'], 
                  default: 'auto' 
                },
              },
            },
          },
          required: ['urls'],
        },
      },
    ];

    this.sendResponse({
      jsonrpc: '2.0',
      id: request.id,
      result: { tools },
    });
  }

  private async handleToolCall(request: MCPRequest): Promise<void> {
    const { name, arguments: args } = request.params;

    try {
      let result: any;

      switch (name) {
        case 'fetch_content':
          result = await this.handleFetchContent(args);
          break;
        case 'batch_fetch_content':
          result = await this.handleBatchFetchContent(args);
          break;
        default:
          this.sendError(request.id, -32601, `Unknown tool: ${name}`);
          return;
      }

      this.sendResponse({
        jsonrpc: '2.0',
        id: request.id,
        result: {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        },
      });
    } catch (error) {
      this.logger.error('Tool execution failed', error as Error, { tool: name, args });
      this.sendError(request.id, -32603, `Tool execution failed: ${(error as Error).message}`);
    }
  }

  private async handleFetchContent(args: any) {
    const { url, options = {} } = args;
    
    // 验证输入
    const validatedOptions = FetchOptionsSchema.partial().parse(options);
    
    return await this.contentFetcher.fetch(url, validatedOptions);
  }

  private async handleBatchFetchContent(args: any) {
    const { urls, options = {} } = args;
    
    if (!Array.isArray(urls)) {
      throw new Error('urls must be an array');
    }

    const validatedOptions = FetchOptionsSchema.partial().parse(options);
    return await this.contentFetcher.batchFetch(urls, validatedOptions);
  }

  private sendResponse(response: MCPResponse): void {
    process.stdout.write(JSON.stringify(response) + '\n');
  }

  private sendError(id: string | number, code: number, message: string, data?: any): void {
    this.sendResponse({
      jsonrpc: '2.0',
      id,
      error: { code, message, data },
    });
  }

  async stop(): Promise<void> {
    await this.contentFetcher.destroy();
    this.proxyManager.destroy();
    this.authManager.destroy();
    this.cacheManager.destroy();
    this.logger.info('MCP Server stopped');
  }
}
