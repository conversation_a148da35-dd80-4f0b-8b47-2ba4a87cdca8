import { z } from 'zod';

// 基础配置类型
export const ProxyConfigSchema = z.object({
  id: z.string(),
  type: z.enum(['http', 'https', 'socks4', 'socks5']),
  host: z.string(),
  port: z.number().min(1).max(65535),
  username: z.string().optional(),
  password: z.string().optional(),
  enabled: z.boolean().default(true),
  priority: z.number().default(1),
  lastChecked: z.date().optional(),
  isHealthy: z.boolean().default(true),
  responseTime: z.number().optional(),
});

export const AuthConfigSchema = z.object({
  type: z.enum(['basic', 'bearer', 'cookie', 'oauth2', 'custom']),
  credentials: z.record(z.string(), z.any()),
  domain: z.string(),
  expiresAt: z.date().optional(),
  refreshToken: z.string().optional(),
});

export const RetryConfigSchema = z.object({
  maxRetries: z.number().min(0).max(10).default(3),
  baseDelay: z.number().min(100).default(1000),
  maxDelay: z.number().min(1000).default(30000),
  backoffFactor: z.number().min(1).default(2),
  retryCondition: z.function().args(z.any()).returns(z.boolean()).optional(),
});

export const CacheStrategySchema = z.object({
  enabled: z.boolean().default(true),
  ttl: z.number().min(0).default(3600), // seconds
  maxSize: z.number().min(0).default(1000),
  strategy: z.enum(['lru', 'fifo', 'ttl']).default('lru'),
});

export const FetchOptionsSchema = z.object({
  useProxy: z.boolean().default(false),
  proxyId: z.string().optional(),
  authConfig: AuthConfigSchema.optional(),
  renderMode: z.enum(['static', 'dynamic', 'auto']).default('auto'),
  retryConfig: RetryConfigSchema.optional(),
  cacheStrategy: CacheStrategySchema.optional(),
  timeout: z.number().min(1000).default(30000),
  userAgent: z.string().optional(),
  headers: z.record(z.string(), z.string()).optional(),
  waitForSelector: z.string().optional(),
  waitForTimeout: z.number().optional(),
});

// 内容相关类型
export const ExtractedContentSchema = z.object({
  title: z.string(),
  content: z.string(),
  textContent: z.string(),
  length: z.number(),
  excerpt: z.string(),
  byline: z.string().optional(),
  dir: z.string().optional(),
  siteName: z.string().optional(),
  lang: z.string().optional(),
  publishedTime: z.string().optional(),
  images: z.array(z.string()).default([]),
  links: z.array(z.object({
    href: z.string(),
    text: z.string(),
  })).default([]),
});

export const StructuredContentSchema = z.object({
  url: z.string().url(),
  title: z.string(),
  content: z.string(),
  markdown: z.string(),
  metadata: z.object({
    author: z.string().optional(),
    publishedAt: z.string().optional(),
    description: z.string().optional(),
    keywords: z.array(z.string()).default([]),
    language: z.string().optional(),
    siteName: z.string().optional(),
  }),
  extractedAt: z.date(),
  processingTime: z.number(),
  qualityScore: z.number().min(0).max(1),
  source: z.enum(['static', 'dynamic']),
});

export const QualityScoreSchema = z.object({
  overall: z.number().min(0).max(1),
  contentLength: z.number().min(0).max(1),
  readability: z.number().min(0).max(1),
  structure: z.number().min(0).max(1),
  metadata: z.number().min(0).max(1),
});

// 缓存相关类型
export const CachedContentSchema = z.object({
  key: z.string(),
  content: StructuredContentSchema,
  createdAt: z.date(),
  expiresAt: z.date(),
  accessCount: z.number().default(0),
  lastAccessed: z.date(),
});

export const CacheStatsSchema = z.object({
  totalEntries: z.number(),
  hitRate: z.number(),
  missRate: z.number(),
  totalHits: z.number(),
  totalMisses: z.number(),
  averageResponseTime: z.number(),
  cacheSize: z.number(),
  oldestEntry: z.date().optional(),
  newestEntry: z.date().optional(),
});

// 服务器配置类型
export const ServerConfigSchema = z.object({
  server: z.object({
    port: z.number().default(3000),
    host: z.string().default('localhost'),
    maxConcurrency: z.number().default(10),
    timeout: z.number().default(30000),
  }),
  proxy: z.object({
    autoDetect: z.boolean().default(true),
    customProxies: z.array(ProxyConfigSchema).default([]),
    healthCheckInterval: z.number().default(300000), // 5 minutes
    rotationStrategy: z.enum(['round-robin', 'random', 'performance']).default('performance'),
  }),
  cache: z.object({
    enabled: z.boolean().default(true),
    maxSize: z.number().default(1000),
    defaultTTL: z.number().default(3600),
    cleanupInterval: z.number().default(3600000), // 1 hour
    dbPath: z.string().default('./data/cache.db'),
  }),
  fetcher: z.object({
    userAgents: z.array(z.string()).default([
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ]),
    defaultHeaders: z.record(z.string(), z.string()).default({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    }),
    retryConfig: RetryConfigSchema,
    rateLimiting: z.object({
      enabled: z.boolean().default(true),
      requestsPerSecond: z.number().default(5),
      burstSize: z.number().default(10),
    }),
  }),
  security: z.object({
    encryptionKey: z.string().min(32),
    authStorePath: z.string().default('./data/auth.db'),
    logLevel: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    maxRequestSize: z.number().default(10485760), // 10MB
  }),
});

// 导出类型
export type ProxyConfig = z.infer<typeof ProxyConfigSchema>;
export type AuthConfig = z.infer<typeof AuthConfigSchema>;
export type RetryConfig = z.infer<typeof RetryConfigSchema>;
export type CacheStrategy = z.infer<typeof CacheStrategySchema>;
export type FetchOptions = z.infer<typeof FetchOptionsSchema>;
export type ExtractedContent = z.infer<typeof ExtractedContentSchema>;
export type StructuredContent = z.infer<typeof StructuredContentSchema>;
export type QualityScore = z.infer<typeof QualityScoreSchema>;
export type CachedContent = z.infer<typeof CachedContentSchema>;
export type CacheStats = z.infer<typeof CacheStatsSchema>;
export type ServerConfig = z.infer<typeof ServerConfigSchema>;

// 错误类型
export class ContentHunterError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public cause?: Error
  ) {
    super(message);
    this.name = 'ContentHunterError';
  }
}

export class ProxyError extends ContentHunterError {
  constructor(message: string, cause?: Error) {
    super(message, 'PROXY_ERROR', 502, cause);
  }
}

export class AuthError extends ContentHunterError {
  constructor(message: string, cause?: Error) {
    super(message, 'AUTH_ERROR', 401, cause);
  }
}

export class FetchError extends ContentHunterError {
  constructor(message: string, statusCode?: number, cause?: Error) {
    super(message, 'FETCH_ERROR', statusCode, cause);
  }
}

export class ProcessingError extends ContentHunterError {
  constructor(message: string, cause?: Error) {
    super(message, 'PROCESSING_ERROR', 500, cause);
  }
}

// MCP工具定义
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: z.ZodSchema;
  handler: (input: any) => Promise<any>;
}

// 批处理相关类型
export interface BatchRequest {
  id: string;
  urls: string[];
  options: FetchOptions;
  priority: number;
  createdAt: Date;
}

export interface BatchResult {
  id: string;
  results: Array<{
    url: string;
    success: boolean;
    content?: StructuredContent;
    error?: string;
  }>;
  completedAt: Date;
  totalTime: number;
}
