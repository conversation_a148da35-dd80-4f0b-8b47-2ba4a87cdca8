import { ServerConfig } from '../types/index.js';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
}

export class Logger {
  private logLevel: LogLevel;
  private context: Record<string, any>;

  constructor(logLevel: LogLevel = 'info', context: Record<string, any> = {}) {
    this.logLevel = logLevel;
    this.context = context;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    };
    return levels[level] >= levels[this.logLevel];
  }

  private formatMessage(level: LogLevel, message: string, context?: Record<string, any>, error?: Error): string {
    const timestamp = new Date().toISOString();
    const levelStr = level.toUpperCase().padEnd(5);
    const contextStr = context ? ` ${JSON.stringify({ ...this.context, ...context })}` : '';
    const errorStr = error ? ` ERROR: ${error.message}\n${error.stack}` : '';
    
    return `[${timestamp}] ${levelStr} ${message}${contextStr}${errorStr}`;
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, context, error);
    
    switch (level) {
      case 'debug':
      case 'info':
        console.log(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
    }
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: Record<string, any>): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log('warn', message, context);
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    this.log('error', message, context, error);
  }

  child(context: Record<string, any>): Logger {
    return new Logger(this.logLevel, { ...this.context, ...context });
  }

  setLevel(level: LogLevel): void {
    this.logLevel = level;
  }
}

// 全局日志实例
let globalLogger: Logger;

export const initLogger = (config: ServerConfig): void => {
  globalLogger = new Logger(config.security.logLevel);
};

export const getLogger = (context?: Record<string, any>): Logger => {
  if (!globalLogger) {
    globalLogger = new Logger();
  }
  return context ? globalLogger.child(context) : globalLogger;
};

// 性能监控装饰器
export function logPerformance(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    const logger = getLogger({ method: `${target.constructor.name}.${propertyName}` });
    const startTime = Date.now();
    
    try {
      logger.debug('Method started', { args: args.length });
      const result = await method.apply(this, args);
      const duration = Date.now() - startTime;
      logger.debug('Method completed', { duration });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Method failed', error as Error, { duration });
      throw error;
    }
  };

  return descriptor;
}

// 错误处理工具
export const logError = (error: Error, context?: Record<string, any>): void => {
  const logger = getLogger(context);
  logger.error(error.message, error, context);
};

// 请求日志中间件
export const logRequest = (url: string, options: any): void => {
  const logger = getLogger({ component: 'fetcher' });
  logger.info('Request started', { 
    url, 
    method: options.method || 'GET',
    userAgent: options.headers?.['User-Agent']?.substring(0, 50) + '...',
  });
};

export const logResponse = (url: string, statusCode: number, duration: number): void => {
  const logger = getLogger({ component: 'fetcher' });
  logger.info('Request completed', { 
    url, 
    statusCode, 
    duration,
    success: statusCode >= 200 && statusCode < 300,
  });
};
