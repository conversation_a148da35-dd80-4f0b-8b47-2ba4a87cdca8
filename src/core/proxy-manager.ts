import { ProxyConfig, ProxyError, ServerConfig } from '../types/index.js';
import { getLogger } from '../utils/logger.js';
import axios, { AxiosProxyConfig } from 'axios';
import { execSync } from 'child_process';
import { platform } from 'os';

export class ProxyManager {
  private proxies: Map<string, ProxyConfig> = new Map();
  private healthCheckInterval?: NodeJS.Timeout | undefined;
  private rotationIndex = 0;
  private logger = getLogger({ component: 'ProxyManager' });
  private config: ServerConfig['proxy'];

  constructor(config: ServerConfig['proxy']) {
    this.config = config;
    this.initializeProxies();
  }

  private async initializeProxies(): Promise<void> {
    // 添加自定义代理
    for (const proxy of this.config.customProxies) {
      this.addProxy(proxy);
    }

    // 自动检测系统代理
    if (this.config.autoDetect) {
      try {
        const systemProxy = await this.detectSystemProxy();
        if (systemProxy) {
          this.addProxy(systemProxy);
          this.logger.info('System proxy detected and added', { proxy: systemProxy.id });
        }
      } catch (error) {
        this.logger.warn('Failed to detect system proxy', { error: (error as Error).message });
      }
    }

    // 启动健康检查
    this.startHealthMonitoring();
  }

  async detectSystemProxy(): Promise<ProxyConfig | null> {
    const os = platform();
    
    try {
      switch (os) {
        case 'win32':
          return this.detectWindowsProxy();
        case 'darwin':
          return this.detectMacOSProxy();
        case 'linux':
          return this.detectLinuxProxy();
        default:
          this.logger.warn('Unsupported OS for proxy detection', { os });
          return null;
      }
    } catch (error) {
      this.logger.error('System proxy detection failed', error as Error);
      return null;
    }
  }

  private detectWindowsProxy(): ProxyConfig | null {
    try {
      const result = execSync('reg query "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer', 
        { encoding: 'utf8' });
      
      const match = result.match(/ProxyServer\s+REG_SZ\s+(.+)/);
      if (match) {
        const proxyString = match[1].trim();
        return this.parseProxyString(proxyString, 'system-windows');
      }
    } catch (error) {
      this.logger.debug('No Windows proxy found');
    }
    return null;
  }

  private detectMacOSProxy(): ProxyConfig | null {
    try {
      const result = execSync('scutil --proxy', { encoding: 'utf8' });
      const lines = result.split('\n');
      
      let host = '';
      let port = 0;
      
      for (const line of lines) {
        if (line.includes('HTTPProxy')) {
          host = line.split(':')[1]?.trim() || '';
        }
        if (line.includes('HTTPPort')) {
          port = parseInt(line.split(':')[1]?.trim() || '0');
        }
      }
      
      if (host && port) {
        return {
          id: 'system-macos',
          type: 'http',
          host,
          port,
          enabled: true,
          priority: 10,
          isHealthy: true,
        };
      }
    } catch (error) {
      this.logger.debug('No macOS proxy found');
    }
    return null;
  }

  private detectLinuxProxy(): ProxyConfig | null {
    const httpProxy = process.env['http_proxy'] || process.env['HTTP_PROXY'];
    const httpsProxy = process.env['https_proxy'] || process.env['HTTPS_PROXY'];
    
    const proxyUrl = httpsProxy || httpProxy;
    if (proxyUrl) {
      return this.parseProxyUrl(proxyUrl, 'system-linux');
    }
    return null;
  }

  private parseProxyString(proxyString: string, id: string): ProxyConfig | null {
    const parts = proxyString.split(':');
    if (parts.length >= 2) {
      return {
        id,
        type: 'http',
        host: parts[0],
        port: parseInt(parts[1]),
        enabled: true,
        priority: 10,
        isHealthy: true,
      };
    }
    return null;
  }

  private parseProxyUrl(url: string, id: string): ProxyConfig | null {
    try {
      const parsed = new URL(url);
      return {
        id,
        type: parsed.protocol.replace(':', '') as ProxyConfig['type'],
        host: parsed.hostname,
        port: parseInt(parsed.port) || (parsed.protocol === 'https:' ? 443 : 80),
        username: parsed.username || undefined,
        password: parsed.password || undefined,
        enabled: true,
        priority: 10,
        isHealthy: true,
      };
    } catch (error) {
      this.logger.warn('Failed to parse proxy URL', { url, error: (error as Error).message });
      return null;
    }
  }

  addProxy(proxy: ProxyConfig): void {
    this.proxies.set(proxy.id, proxy);
    this.logger.info('Proxy added', { proxyId: proxy.id, host: proxy.host, port: proxy.port });
  }

  removeProxy(proxyId: string): void {
    if (this.proxies.delete(proxyId)) {
      this.logger.info('Proxy removed', { proxyId });
    }
  }

  async getHealthyProxy(target?: string): Promise<ProxyConfig | null> {
    const healthyProxies = Array.from(this.proxies.values())
      .filter(proxy => proxy.enabled && proxy.isHealthy)
      .sort((a, b) => b.priority - a.priority);

    if (healthyProxies.length === 0) {
      return null;
    }

    switch (this.config.rotationStrategy) {
      case 'round-robin':
        return this.getRoundRobinProxy(healthyProxies);
      case 'random':
        return this.getRandomProxy(healthyProxies);
      case 'performance':
        return this.getPerformanceBasedProxy(healthyProxies);
      default:
        return healthyProxies[0] || null;
    }
  }

  private getRoundRobinProxy(proxies: ProxyConfig[]): ProxyConfig | null {
    if (proxies.length === 0) return null;
    const proxy = proxies[this.rotationIndex % proxies.length];
    this.rotationIndex++;
    return proxy;
  }

  private getRandomProxy(proxies: ProxyConfig[]): ProxyConfig | null {
    if (proxies.length === 0) return null;
    const index = Math.floor(Math.random() * proxies.length);
    return proxies[index] || null;
  }

  private getPerformanceBasedProxy(proxies: ProxyConfig[]): ProxyConfig {
    // 选择响应时间最短的代理
    return proxies.reduce((best, current) => {
      const bestTime = best.responseTime || Infinity;
      const currentTime = current.responseTime || Infinity;
      return currentTime < bestTime ? current : best;
    });
  }

  async checkProxyHealth(proxy: ProxyConfig): Promise<boolean> {
    const startTime = Date.now();
    
    try {
      const axiosProxy: AxiosProxyConfig = {
        protocol: proxy.type,
        host: proxy.host,
        port: proxy.port,
        ...(proxy.username && proxy.password ? {
          auth: {
            username: proxy.username,
            password: proxy.password,
          }
        } : {}),
      };

      await axios.get('https://httpbin.org/ip', {
        proxy: axiosProxy,
        timeout: 10000,
        validateStatus: (status) => status === 200,
      });

      const responseTime = Date.now() - startTime;
      
      // 更新代理状态
      proxy.isHealthy = true;
      proxy.responseTime = responseTime;
      proxy.lastChecked = new Date();

      this.logger.debug('Proxy health check passed', { 
        proxyId: proxy.id, 
        responseTime 
      });

      return true;
    } catch (error) {
      proxy.isHealthy = false;
      proxy.lastChecked = new Date();
      
      this.logger.warn('Proxy health check failed', { 
        proxyId: proxy.id, 
        error: (error as Error).message 
      });

      return false;
    }
  }

  startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      const proxies = Array.from(this.proxies.values());
      const healthChecks = proxies.map(proxy => this.checkProxyHealth(proxy));
      
      try {
        await Promise.allSettled(healthChecks);
        this.logger.debug('Health check completed for all proxies');
      } catch (error) {
        this.logger.error('Health check batch failed', error as Error);
      }
    }, this.config.healthCheckInterval);

    this.logger.info('Proxy health monitoring started', { 
      interval: this.config.healthCheckInterval 
    });
  }

  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
      this.logger.info('Proxy health monitoring stopped');
    }
  }

  getProxyStats(): { total: number; healthy: number; enabled: number } {
    const proxies = Array.from(this.proxies.values());
    return {
      total: proxies.length,
      healthy: proxies.filter(p => p.isHealthy).length,
      enabled: proxies.filter(p => p.enabled).length,
    };
  }

  toAxiosProxy(proxy: ProxyConfig): AxiosProxyConfig {
    return {
      protocol: proxy.type,
      host: proxy.host,
      port: proxy.port,
      ...(proxy.username && proxy.password ? {
        auth: {
          username: proxy.username,
          password: proxy.password,
        }
      } : {}),
    };
  }

  destroy(): void {
    this.stopHealthMonitoring();
    this.proxies.clear();
  }
}
