import { ExtractedContent, StructuredContent, QualityScore, ProcessingError } from '../types/index.js';
import { getLogger, logPerformance } from '../utils/logger.js';
import { Readability } from '@mozilla/readability';
import { JSDOM } from 'jsdom';
import TurndownService from 'turndown';

export class ContentProcessor {
  private logger = getLogger({ component: 'ContentProcessor' });
  private turndownService: TurndownService;

  constructor() {
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      hr: '---',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '_',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
    });

    // 自定义转换规则
    this.setupTurndownRules();
  }

  private setupTurndownRules(): void {
    // 处理图片
    this.turndownService.addRule('images', {
      filter: 'img',
      replacement: (content, node) => {
        const img = node as HTMLImageElement;
        const alt = img.alt || '';
        const src = img.src || '';
        const title = img.title ? ` "${img.title}"` : '';
        return src ? `![${alt}](${src}${title})` : '';
      },
    });

    // 处理代码块
    this.turndownService.addRule('codeBlocks', {
      filter: ['pre'],
      replacement: (content, node) => {
        const pre = node as HTMLPreElement;
        const code = pre.querySelector('code');
        const language = code?.className.match(/language-(\w+)/)?.[1] || '';
        return `\n\`\`\`${language}\n${content}\n\`\`\`\n`;
      },
    });

    // 处理表格
    this.turndownService.addRule('tables', {
      filter: 'table',
      replacement: (content, node) => {
        const table = node as HTMLTableElement;
        const rows = Array.from(table.querySelectorAll('tr'));
        
        if (rows.length === 0) return '';

        let markdown = '\n';
        
        rows.forEach((row, index) => {
          const cells = Array.from(row.querySelectorAll('td, th'));
          const cellContents = cells.map(cell => cell.textContent?.trim() || '');
          markdown += `| ${cellContents.join(' | ')} |\n`;
          
          // 添加表头分隔符
          if (index === 0 && row.querySelector('th')) {
            const separator = cells.map(() => '---').join(' | ');
            markdown += `| ${separator} |\n`;
          }
        });
        
        return markdown + '\n';
      },
    });
  }

  async extractContent(html: string, url: string): Promise<ExtractedContent> {
    try {
      const dom = new JSDOM(html, { url });
      const document = dom.window.document;

      // 使用 Readability 提取主要内容
      const reader = new Readability(document);
      const article = reader.parse();

      if (!article) {
        throw new ProcessingError('Failed to extract readable content from HTML');
      }

      // 提取额外的元数据
      const images = this.extractImages(document);
      const links = this.extractLinks(document);

      const extractedContent: ExtractedContent = {
        title: article.title,
        content: article.content,
        textContent: article.textContent,
        length: article.length,
        excerpt: article.excerpt,
        byline: article.byline || undefined,
        dir: article.dir || undefined,
        siteName: article.siteName || undefined,
        lang: article.lang || undefined,
        publishedTime: article.publishedTime || undefined,
        images,
        links,
      };

      this.logger.debug('Content extracted successfully', {
        url,
        title: article.title,
        length: article.length,
        imagesCount: images.length,
        linksCount: links.length,
      });

      return extractedContent;
    } catch (error) {
      this.logger.error('Content extraction failed', error as Error, { url });
      throw new ProcessingError(`Failed to extract content from ${url}`, error as Error);
    }
  }

  private extractImages(document: Document): string[] {
    const images: string[] = [];
    const imgElements = document.querySelectorAll('img');
    
    imgElements.forEach(img => {
      const src = img.src || img.getAttribute('data-src');
      if (src && this.isValidImageUrl(src)) {
        images.push(src);
      }
    });

    return [...new Set(images)]; // 去重
  }

  private extractLinks(document: Document): Array<{ href: string; text: string }> {
    const links: Array<{ href: string; text: string }> = [];
    const linkElements = document.querySelectorAll('a[href]');
    
    linkElements.forEach(link => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim();
      
      if (href && text && this.isValidLinkUrl(href)) {
        links.push({ href, text });
      }
    });

    return links;
  }

  private isValidImageUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
      return imageExtensions.some(ext => 
        parsed.pathname.toLowerCase().includes(ext)
      ) || parsed.pathname.includes('image');
    } catch {
      return false;
    }
  }

  private isValidLinkUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      // 相对链接也是有效的
      return url.startsWith('/') || url.startsWith('#');
    }
  }

  toMarkdown(content: ExtractedContent): string {
    try {
      return this.turndownService.turndown(content.content);
    } catch (error) {
      this.logger.error('Markdown conversion failed', error as Error);
      // 降级处理：返回纯文本
      return content.textContent;
    }
  }

  toJSON(content: ExtractedContent): StructuredContent {
    const markdown = this.toMarkdown(content);
    const qualityScore = this.assessQuality(content);
    
    return {
      url: '', // 将由调用者设置
      title: content.title,
      content: content.content,
      markdown,
      metadata: {
        author: content.byline,
        publishedAt: content.publishedTime,
        description: content.excerpt,
        keywords: this.extractKeywords(content.textContent),
        language: content.lang,
        siteName: content.siteName,
      },
      extractedAt: new Date(),
      processingTime: 0, // 将由调用者设置
      qualityScore: qualityScore.overall,
      source: 'static', // 将由调用者设置
    };
  }

  assessQuality(content: ExtractedContent): QualityScore {
    // 内容长度评分 (0-1)
    const contentLength = Math.min(content.length / 1000, 1); // 1000字符为满分
    
    // 可读性评分 (基于简单指标)
    const readability = this.calculateReadabilityScore(content.textContent);
    
    // 结构评分 (基于HTML结构)
    const structure = this.calculateStructureScore(content.content);
    
    // 元数据完整性评分
    const metadata = this.calculateMetadataScore(content);
    
    // 综合评分
    const overall = (contentLength * 0.3 + readability * 0.3 + structure * 0.2 + metadata * 0.2);
    
    return {
      overall: Math.round(overall * 100) / 100,
      contentLength: Math.round(contentLength * 100) / 100,
      readability: Math.round(readability * 100) / 100,
      structure: Math.round(structure * 100) / 100,
      metadata: Math.round(metadata * 100) / 100,
    };
  }

  private calculateReadabilityScore(text: string): number {
    if (!text || text.length < 100) return 0;
    
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const avgWordsPerSentence = words.length / sentences.length;
    
    // 理想的句子长度是15-20个词
    const sentenceLengthScore = Math.max(0, 1 - Math.abs(avgWordsPerSentence - 17.5) / 17.5);
    
    // 词汇多样性
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    const vocabularyDiversity = uniqueWords.size / words.length;
    
    return (sentenceLengthScore + vocabularyDiversity) / 2;
  }

  private calculateStructureScore(html: string): number {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      
      let score = 0;
      
      // 检查标题结构
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      if (headings.length > 0) score += 0.3;
      
      // 检查段落结构
      const paragraphs = document.querySelectorAll('p');
      if (paragraphs.length > 2) score += 0.3;
      
      // 检查列表
      const lists = document.querySelectorAll('ul, ol');
      if (lists.length > 0) score += 0.2;
      
      // 检查链接
      const links = document.querySelectorAll('a[href]');
      if (links.length > 0) score += 0.1;
      
      // 检查图片
      const images = document.querySelectorAll('img');
      if (images.length > 0) score += 0.1;
      
      return Math.min(score, 1);
    } catch {
      return 0.5; // 默认中等评分
    }
  }

  private calculateMetadataScore(content: ExtractedContent): number {
    let score = 0;
    
    if (content.title && content.title.length > 10) score += 0.3;
    if (content.byline) score += 0.2;
    if (content.publishedTime) score += 0.2;
    if (content.excerpt && content.excerpt.length > 50) score += 0.2;
    if (content.siteName) score += 0.1;
    
    return Math.min(score, 1);
  }

  private extractKeywords(text: string): string[] {
    if (!text || text.length < 100) return [];
    
    // 简单的关键词提取算法
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // 计算词频
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });
    
    // 排序并取前10个
    const sortedWords = Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
    
    return sortedWords;
  }

  // 清理HTML内容
  sanitizeHtml(html: string): string {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      
      // 移除脚本和样式
      const scripts = document.querySelectorAll('script, style');
      scripts.forEach(el => el.remove());
      
      // 移除注释
      const walker = document.createTreeWalker(
        document.body,
        dom.window.NodeFilter.SHOW_COMMENT
      );
      
      const comments: Node[] = [];
      let node;
      while (node = walker.nextNode()) {
        comments.push(node);
      }
      comments.forEach(comment => comment.remove());
      
      return document.body.innerHTML;
    } catch (error) {
      this.logger.warn('HTML sanitization failed', { error: (error as Error).message });
      return html;
    }
  }
}
