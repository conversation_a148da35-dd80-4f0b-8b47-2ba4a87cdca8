import { FetchOptions, StructuredContent, FetchError, RetryConfig } from '../types/index.js';
import { getLogger, logRequest, logResponse } from '../utils/logger.js';
import { ProxyManager } from './proxy-manager.js';
import { AuthManager } from './auth-manager.js';
import { CacheManager } from './cache-manager.js';
import { ContentProcessor } from './content-processor.js';
import axios, { AxiosRequestConfig } from 'axios';
import { chromium, Browser, Page } from 'playwright';
import { URL } from 'url';

export class ContentFetcher {
  private logger = getLogger({ component: 'ContentFetcher' });
  private proxyManager: ProxyManager;
  private authManager: AuthManager;
  private cacheManager: CacheManager;
  private contentProcessor: ContentProcessor;
  private browser?: Browser;
  private userAgents: string[];
  private defaultHeaders: Record<string, string>;

  constructor(
    proxyManager: ProxyManager,
    authManager: AuthManager,
    cacheManager: CacheManager,
    contentProcessor: ContentProcessor,
    config: { userAgents: string[]; defaultHeaders: Record<string, string> }
  ) {
    this.proxyManager = proxyManager;
    this.authManager = authManager;
    this.cacheManager = cacheManager;
    this.contentProcessor = contentProcessor;
    this.userAgents = config.userAgents;
    this.defaultHeaders = config.defaultHeaders;
  }

  async fetch(url: string, options: Partial<FetchOptions> = {}): Promise<StructuredContent> {
    const startTime = Date.now();
    
    try {
      // 验证URL
      this.validateUrl(url);
      
      // 检查缓存
      if (options.cacheStrategy?.enabled !== false) {
        const cached = await this.cacheManager.getByUrl(url, options);
        if (cached) {
          this.logger.info('Content served from cache', { url });
          return cached.content;
        }
      }

      // 创建完整的options对象
      const fullOptions: FetchOptions = {
        useProxy: options.useProxy ?? false,
        renderMode: options.renderMode ?? 'auto',
        timeout: options.timeout ?? 30000,
        proxyId: options.proxyId,
        authConfig: options.authConfig,
        retryConfig: options.retryConfig,
        cacheStrategy: options.cacheStrategy,
        userAgent: options.userAgent,
        headers: options.headers,
        waitForSelector: options.waitForSelector,
        waitForTimeout: options.waitForTimeout,
      };

      // 确定抓取策略
      const renderMode = await this.determineRenderMode(url, fullOptions.renderMode);

      // 获取内容
      let html: string;
      if (renderMode === 'dynamic') {
        html = await this.fetchDynamic(url, fullOptions);
      } else {
        html = await this.fetchStatic(url, fullOptions);
      }

      // 处理内容
      const extractedContent = await this.contentProcessor.extractContent(html, url);
      const structuredContent = this.contentProcessor.toJSON(extractedContent);
      
      // 设置元数据
      structuredContent.url = url;
      structuredContent.processingTime = Date.now() - startTime;
      structuredContent.source = renderMode;

      // 缓存结果
      if (options.cacheStrategy?.enabled !== false) {
        await this.cacheManager.setByUrl(
          url, 
          structuredContent, 
          options, 
          options.cacheStrategy?.ttl
        );
      }

      this.logger.info('Content fetched successfully', {
        url,
        renderMode,
        processingTime: structuredContent.processingTime,
        qualityScore: structuredContent.qualityScore,
      });

      return structuredContent;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('Content fetch failed', error as Error, { url, processingTime });
      throw error;
    }
  }

  async batchFetch(urls: string[], options: Partial<FetchOptions> = {}): Promise<StructuredContent[]> {
    const results: StructuredContent[] = [];
    const concurrency = 5; // 限制并发数
    
    this.logger.info('Starting batch fetch', { urlCount: urls.length, concurrency });

    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      const batchPromises = batch.map(async (url) => {
        try {
          return await this.fetch(url, options);
        } catch (error) {
          this.logger.warn('Batch fetch item failed', { url, error: (error as Error).message });
          return null;
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value);
        }
      });

      // 添加延迟以避免过于频繁的请求
      if (i + concurrency < urls.length) {
        await this.delay(1000);
      }
    }

    this.logger.info('Batch fetch completed', { 
      total: urls.length, 
      successful: results.length,
      failed: urls.length - results.length,
    });

    return results;
  }

  private validateUrl(url: string): void {
    try {
      const parsed = new URL(url);
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        throw new FetchError(`Unsupported protocol: ${parsed.protocol}`);
      }
    } catch (error) {
      throw new FetchError(`Invalid URL: ${url}`, 400, error as Error);
    }
  }

  private async determineRenderMode(url: string, mode?: 'static' | 'dynamic' | 'auto'): Promise<'static' | 'dynamic'> {
    if (mode === 'static' || mode === 'dynamic') {
      return mode;
    }

    // 自动检测逻辑
    try {
      const domain = new URL(url).hostname;
      
      // 已知需要动态渲染的网站
      const dynamicSites = [
        'spa-app.com',
        'react-app.com',
        'vue-app.com',
        'angular-app.com',
      ];

      if (dynamicSites.some(site => domain.includes(site))) {
        return 'dynamic';
      }

      // 默认使用静态抓取
      return 'static';
    } catch {
      return 'static';
    }
  }

  private async fetchStatic(url: string, options: FetchOptions): Promise<string> {
    const config = await this.buildAxiosConfig(url, options);
    
    return this.executeWithRetry(async () => {
      logRequest(url, config);
      const startTime = Date.now();
      
      const response = await axios.get(url, config);
      
      logResponse(url, response.status, Date.now() - startTime);
      
      if (response.status !== 200) {
        throw new FetchError(`HTTP ${response.status}: ${response.statusText}`, response.status);
      }

      return response.data;
    }, options.retryConfig);
  }

  private async fetchDynamic(url: string, options: FetchOptions): Promise<string> {
    if (!this.browser) {
      this.browser = await chromium.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
    }

    const context = await this.browser.newContext({
      userAgent: this.getRandomUserAgent(),
      viewport: { width: 1920, height: 1080 },
    });

    let page: Page | undefined;

    try {
      page = await context.newPage();

      // 设置代理
      if (options.useProxy) {
        const proxy = await this.proxyManager.getHealthyProxy(url);
        if (proxy) {
          await context.setExtraHTTPHeaders({
            'Proxy-Authorization': proxy.username && proxy.password 
              ? `Basic ${Buffer.from(`${proxy.username}:${proxy.password}`).toString('base64')}`
              : '',
          });
        }
      }

      // 设置认证
      const domain = new URL(url).hostname;
      const authHeaders = await this.authManager.generateAuthHeaders(domain);
      if (Object.keys(authHeaders).length > 0) {
        await context.setExtraHTTPHeaders(authHeaders);
      }

      // 导航到页面
      await page.goto(url, { 
        waitUntil: 'networkidle',
        timeout: options.timeout || 30000,
      });

      // 等待特定选择器（如果指定）
      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, {
          timeout: options.waitForTimeout || 10000,
        });
      }

      // 获取页面内容
      const html = await page.content();
      
      return html;
    } finally {
      if (page) {
        await page.close();
      }
      await context.close();
    }
  }

  private async buildAxiosConfig(url: string, options: FetchOptions): Promise<AxiosRequestConfig> {
    const config: AxiosRequestConfig = {
      timeout: options.timeout || 30000,
      headers: {
        ...this.defaultHeaders,
        'User-Agent': options.userAgent || this.getRandomUserAgent(),
        ...options.headers,
      },
      maxRedirects: 5,
      validateStatus: (status) => status < 400,
    };

    // 设置代理
    if (options.useProxy) {
      const proxy = await this.proxyManager.getHealthyProxy(url);
      if (proxy) {
        config.proxy = this.proxyManager.toAxiosProxy(proxy);
      }
    }

    // 设置认证
    const domain = new URL(url).hostname;
    const authHeaders = await this.authManager.generateAuthHeaders(domain);
    if (config.headers && authHeaders) {
      Object.assign(config.headers, authHeaders);
    }

    return config;
  }

  private getRandomUserAgent(): string {
    if (this.userAgents.length === 0) {
      return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
    }
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)]!;
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    retryConfig?: RetryConfig
  ): Promise<T> {
    const config = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      ...retryConfig,
    };

    let lastError: Error;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === config.maxRetries) {
          break;
        }

        // 检查是否应该重试
        if (!this.shouldRetry(error as Error)) {
          break;
        }

        // 计算延迟时间
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffFactor, attempt),
          config.maxDelay
        );

        this.logger.warn('Operation failed, retrying', {
          attempt: attempt + 1,
          maxRetries: config.maxRetries,
          delay,
          error: (error as Error).message,
        });

        await this.delay(delay);
      }
    }

    throw lastError;
  }

  private shouldRetry(error: Error): boolean {
    // 网络错误通常可以重试
    if (error.message.includes('ECONNRESET') || 
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('ENOTFOUND')) {
      return true;
    }

    // HTTP 5xx 错误可以重试
    if (error instanceof FetchError && error.statusCode && error.statusCode >= 500) {
      return true;
    }

    // 429 (Too Many Requests) 可以重试
    if (error instanceof FetchError && error.statusCode === 429) {
      return true;
    }

    return false;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async destroy(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = undefined;
    }
    this.logger.info('Content fetcher destroyed');
  }
}
