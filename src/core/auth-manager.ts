import { AuthConfig, AuthError, ServerConfig } from '../types/index.js';
import { getLogger } from '../utils/logger.js';
import { createCipher, createDecipher, randomBytes } from 'crypto';
import Database from 'better-sqlite3';
import { existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';

interface StoredAuth {
  domain: string;
  encrypted_data: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export class AuthManager {
  private db: Database.Database;
  private encryptionKey: string;
  private logger = getLogger({ component: 'AuthManager' });

  constructor(config: ServerConfig['security']) {
    this.encryptionKey = config.encryptionKey;
    
    // 确保数据库目录存在
    const dbDir = dirname(config.authStorePath);
    if (!existsSync(dbDir)) {
      mkdirSync(dbDir, { recursive: true });
    }

    this.db = new Database(config.authStorePath);
    this.initializeDatabase();
  }

  private initializeDatabase(): void {
    // 创建认证表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS auth_configs (
        domain TEXT PRIMARY KEY,
        encrypted_data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        expires_at TEXT
      )
    `);

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_auth_expires_at ON auth_configs(expires_at)
    `);

    this.logger.info('Auth database initialized');
  }

  async setAuth(domain: string, authConfig: AuthConfig): Promise<void> {
    try {
      const encryptedData = await this.encryptStore(authConfig);
      const now = new Date().toISOString();
      const expiresAt = authConfig.expiresAt?.toISOString();

      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO auth_configs 
        (domain, encrypted_data, created_at, updated_at, expires_at)
        VALUES (?, ?, ?, ?, ?)
      `);

      stmt.run(domain, encryptedData, now, now, expiresAt);

      this.logger.info('Auth config stored', { domain, type: authConfig.type });
    } catch (error) {
      this.logger.error('Failed to store auth config', error as Error, { domain });
      throw new AuthError(`Failed to store auth config for ${domain}`, error as Error);
    }
  }

  async getAuth(domain: string): Promise<AuthConfig | null> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM auth_configs WHERE domain = ?
      `);

      const row = stmt.get(domain) as StoredAuth | undefined;
      
      if (!row) {
        return null;
      }

      // 检查是否过期
      if (row.expires_at) {
        const expiresAt = new Date(row.expires_at);
        if (expiresAt < new Date()) {
          this.logger.info('Auth config expired, removing', { domain });
          await this.removeAuth(domain);
          return null;
        }
      }

      const authConfig = await this.decryptStore(row.encrypted_data) as AuthConfig;
      
      this.logger.debug('Auth config retrieved', { domain, type: authConfig.type });
      return authConfig;
    } catch (error) {
      this.logger.error('Failed to retrieve auth config', error as Error, { domain });
      throw new AuthError(`Failed to retrieve auth config for ${domain}`, error as Error);
    }
  }

  async removeAuth(domain: string): Promise<void> {
    try {
      const stmt = this.db.prepare(`DELETE FROM auth_configs WHERE domain = ?`);
      const result = stmt.run(domain);
      
      if (result.changes > 0) {
        this.logger.info('Auth config removed', { domain });
      }
    } catch (error) {
      this.logger.error('Failed to remove auth config', error as Error, { domain });
      throw new AuthError(`Failed to remove auth config for ${domain}`, error as Error);
    }
  }

  async validateAuth(domain: string): Promise<boolean> {
    try {
      const authConfig = await this.getAuth(domain);
      if (!authConfig) {
        return false;
      }

      // 基本验证逻辑
      switch (authConfig.type) {
        case 'basic':
          return !!(authConfig.credentials['username'] && authConfig.credentials['password']);
        case 'bearer':
          return !!authConfig.credentials['token'];
        case 'cookie':
          return !!authConfig.credentials['cookies'];
        case 'oauth2':
          return !!(authConfig.credentials['accessToken'] || authConfig.refreshToken);
        case 'custom':
          return Object.keys(authConfig.credentials).length > 0;
        default:
          return false;
      }
    } catch (error) {
      this.logger.error('Auth validation failed', error as Error, { domain });
      return false;
    }
  }

  async refreshAuth(domain: string): Promise<void> {
    try {
      const authConfig = await this.getAuth(domain);
      if (!authConfig) {
        throw new AuthError(`No auth config found for ${domain}`);
      }

      if (authConfig.type === 'oauth2' && authConfig.refreshToken) {
        // 这里应该实现OAuth2刷新逻辑
        // 由于这是一个通用的实现，我们只是更新时间戳
        authConfig.expiresAt = new Date(Date.now() + 3600000); // 1小时后过期
        await this.setAuth(domain, authConfig);
        
        this.logger.info('Auth refreshed', { domain });
      } else {
        this.logger.warn('Auth refresh not supported for this type', { 
          domain, 
          type: authConfig.type 
        });
      }
    } catch (error) {
      this.logger.error('Auth refresh failed', error as Error, { domain });
      throw new AuthError(`Failed to refresh auth for ${domain}`, error as Error);
    }
  }

  async encryptStore(data: any): Promise<string> {
    try {
      const iv = randomBytes(16);
      const cipher = createCipher('aes-256-cbc', this.encryptionKey);
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Encryption failed', error as Error);
      throw new AuthError('Failed to encrypt data', error as Error);
    }
  }

  async decryptStore(encryptedData: string): Promise<any> {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 2) {
        throw new Error('Invalid encrypted data format');
      }

      const decipher = createDecipher('aes-256-cbc', this.encryptionKey);
      
      if (!parts[1]) {
        throw new Error('Invalid encrypted data format');
      }
      let decrypted = decipher.update(parts[1], 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      this.logger.error('Decryption failed', error as Error);
      throw new AuthError('Failed to decrypt data', error as Error);
    }
  }

  // 清理过期的认证配置
  async cleanupExpiredAuth(): Promise<number> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM auth_configs 
        WHERE expires_at IS NOT NULL AND expires_at < ?
      `);

      const result = stmt.run(new Date().toISOString());
      const deletedCount = result.changes;

      if (deletedCount > 0) {
        this.logger.info('Expired auth configs cleaned up', { count: deletedCount });
      }

      return deletedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired auth configs', error as Error);
      return 0;
    }
  }

  // 获取所有域名列表
  getAllDomains(): string[] {
    try {
      const stmt = this.db.prepare(`SELECT domain FROM auth_configs`);
      const rows = stmt.all() as { domain: string }[];
      return rows.map(row => row.domain);
    } catch (error) {
      this.logger.error('Failed to get domains list', error as Error);
      return [];
    }
  }

  // 获取统计信息
  getStats(): { total: number; expired: number; byType: Record<string, number> } {
    try {
      const totalStmt = this.db.prepare(`SELECT COUNT(*) as count FROM auth_configs`);
      const total = (totalStmt.get() as { count: number }).count;

      const expiredStmt = this.db.prepare(`
        SELECT COUNT(*) as count FROM auth_configs 
        WHERE expires_at IS NOT NULL AND expires_at < ?
      `);
      const expired = (expiredStmt.get(new Date().toISOString()) as { count: number }).count;

      // 由于数据是加密的，我们无法直接统计类型，这里返回基本统计
      return {
        total,
        expired,
        byType: {}, // 需要解密才能统计，出于性能考虑暂不实现
      };
    } catch (error) {
      this.logger.error('Failed to get auth stats', error as Error);
      return { total: 0, expired: 0, byType: {} };
    }
  }

  // 生成认证头
  async generateAuthHeaders(domain: string): Promise<Record<string, string>> {
    const authConfig = await this.getAuth(domain);
    if (!authConfig) {
      return {};
    }

    const headers: Record<string, string> = {};

    switch (authConfig.type) {
      case 'basic':
        const basicAuth = Buffer.from(
          `${authConfig.credentials['username']}:${authConfig.credentials['password']}`
        ).toString('base64');
        headers['Authorization'] = `Basic ${basicAuth}`;
        break;

      case 'bearer':
        headers['Authorization'] = `Bearer ${authConfig.credentials['token']}`;
        break;

      case 'cookie':
        if (authConfig.credentials['cookies']) {
          headers['Cookie'] = authConfig.credentials['cookies'];
        }
        break;

      case 'custom':
        Object.assign(headers, authConfig.credentials);
        break;
    }

    return headers;
  }

  destroy(): void {
    try {
      this.db.close();
      this.logger.info('Auth manager destroyed');
    } catch (error) {
      this.logger.error('Failed to close auth database', error as Error);
    }
  }
}
