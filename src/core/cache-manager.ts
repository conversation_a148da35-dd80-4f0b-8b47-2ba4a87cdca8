import { CachedContent, CacheStats, StructuredContent, ServerConfig } from '../types/index.js';
import { getLogger } from '../utils/logger.js';
import Database from 'better-sqlite3';
import { existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
import { createHash } from 'crypto';

interface CacheEntry {
  key: string;
  content: string;
  created_at: string;
  expires_at: string;
  access_count: number;
  last_accessed: string;
  size: number;
}

export class CacheManager {
  private db!: Database.Database;
  private config: ServerConfig['cache'];
  private logger = getLogger({ component: 'CacheManager' });
  private memoryCache = new Map<string, CachedContent>();
  private cleanupInterval?: NodeJS.Timeout;

  constructor(config: ServerConfig['cache']) {
    this.config = config;
    
    if (!config.enabled) {
      this.logger.info('Cache is disabled');
      return;
    }

    // 确保数据库目录存在
    const dbDir = dirname(config.dbPath);
    if (!existsSync(dbDir)) {
      mkdirSync(dbDir, { recursive: true });
    }

    this.db = new Database(config.dbPath);
    this.initializeDatabase();
    this.startCleanupScheduler();
  }

  private initializeDatabase(): void {
    // 创建缓存表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS cache_entries (
        key TEXT PRIMARY KEY,
        content TEXT NOT NULL,
        created_at TEXT NOT NULL,
        expires_at TEXT NOT NULL,
        access_count INTEGER DEFAULT 0,
        last_accessed TEXT NOT NULL,
        size INTEGER NOT NULL
      )
    `);

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON cache_entries(expires_at);
      CREATE INDEX IF NOT EXISTS idx_cache_last_accessed ON cache_entries(last_accessed);
      CREATE INDEX IF NOT EXISTS idx_cache_size ON cache_entries(size);
    `);

    this.logger.info('Cache database initialized');
  }

  private generateKey(url: string, options?: any): string {
    const data = JSON.stringify({ url, options });
    return createHash('sha256').update(data).digest('hex');
  }

  async get(key: string): Promise<CachedContent | null> {
    if (!this.config.enabled) {
      return null;
    }

    try {
      // 先检查内存缓存
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && memoryEntry.expiresAt > new Date()) {
        this.updateAccessStats(key);
        this.logger.debug('Cache hit (memory)', { key });
        return memoryEntry;
      }

      // 检查磁盘缓存
      const stmt = this.db.prepare(`
        SELECT * FROM cache_entries WHERE key = ? AND expires_at > ?
      `);

      const row = stmt.get(key, new Date().toISOString()) as CacheEntry | undefined;
      
      if (!row) {
        this.logger.debug('Cache miss', { key });
        return null;
      }

      const cachedContent: CachedContent = {
        key: row.key,
        content: JSON.parse(row.content),
        createdAt: new Date(row.created_at),
        expiresAt: new Date(row.expires_at),
        accessCount: row.access_count,
        lastAccessed: new Date(row.last_accessed),
      };

      // 更新访问统计
      this.updateAccessStats(key);

      // 添加到内存缓存
      this.memoryCache.set(key, cachedContent);

      this.logger.debug('Cache hit (disk)', { key });
      return cachedContent;
    } catch (error) {
      this.logger.error('Cache get failed', error as Error, { key });
      return null;
    }
  }

  async set(key: string, content: StructuredContent, ttl?: number): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + (ttl || this.config.defaultTTL) * 1000);
      const contentStr = JSON.stringify(content);
      const size = Buffer.byteLength(contentStr, 'utf8');

      const cachedContent: CachedContent = {
        key,
        content,
        createdAt: now,
        expiresAt,
        accessCount: 0,
        lastAccessed: now,
      };

      // 检查缓存大小限制
      await this.ensureCacheSize();

      // 存储到磁盘
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO cache_entries 
        (key, content, created_at, expires_at, access_count, last_accessed, size)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        key,
        contentStr,
        now.toISOString(),
        expiresAt.toISOString(),
        0,
        now.toISOString(),
        size
      );

      // 添加到内存缓存
      this.memoryCache.set(key, cachedContent);

      this.logger.debug('Cache set', { key, size, ttl });
    } catch (error) {
      this.logger.error('Cache set failed', error as Error, { key });
    }
  }

  async delete(key: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      // 从内存缓存删除
      this.memoryCache.delete(key);

      // 从磁盘缓存删除
      const stmt = this.db.prepare(`DELETE FROM cache_entries WHERE key = ?`);
      const result = stmt.run(key);

      if (result.changes > 0) {
        this.logger.debug('Cache entry deleted', { key });
      }
    } catch (error) {
      this.logger.error('Cache delete failed', error as Error, { key });
    }
  }

  async clear(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      // 清空内存缓存
      this.memoryCache.clear();

      // 清空磁盘缓存
      this.db.exec(`DELETE FROM cache_entries`);

      this.logger.info('Cache cleared');
    } catch (error) {
      this.logger.error('Cache clear failed', error as Error);
    }
  }

  async getStats(): Promise<CacheStats> {
    if (!this.config.enabled) {
      return {
        totalEntries: 0,
        hitRate: 0,
        missRate: 0,
        totalHits: 0,
        totalMisses: 0,
        averageResponseTime: 0,
        cacheSize: 0,
      };
    }

    try {
      const totalStmt = this.db.prepare(`SELECT COUNT(*) as count FROM cache_entries`);
      const totalEntries = (totalStmt.get() as { count: number }).count;

      const sizeStmt = this.db.prepare(`SELECT SUM(size) as size FROM cache_entries`);
      const cacheSize = (sizeStmt.get() as { size: number | null }).size || 0;

      const hitsStmt = this.db.prepare(`SELECT SUM(access_count) as hits FROM cache_entries`);
      const totalHits = (hitsStmt.get() as { hits: number | null }).hits || 0;

      // 这里简化处理，实际应该维护单独的统计表
      const totalMisses = Math.max(0, totalHits * 0.2); // 假设20%的miss率
      const hitRate = totalHits / (totalHits + totalMisses);
      const missRate = 1 - hitRate;

      const oldestStmt = this.db.prepare(`
        SELECT MIN(created_at) as oldest FROM cache_entries
      `);
      const oldestResult = oldestStmt.get() as { oldest: string | null };

      const newestStmt = this.db.prepare(`
        SELECT MAX(created_at) as newest FROM cache_entries
      `);
      const newestResult = newestStmt.get() as { newest: string | null };

      return {
        totalEntries,
        hitRate,
        missRate,
        totalHits,
        totalMisses,
        averageResponseTime: 0, // 需要单独统计
        cacheSize,
        oldestEntry: oldestResult.oldest ? new Date(oldestResult.oldest) : undefined,
        newestEntry: newestResult.newest ? new Date(newestResult.newest) : undefined,
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats', error as Error);
      return {
        totalEntries: 0,
        hitRate: 0,
        missRate: 0,
        totalHits: 0,
        totalMisses: 0,
        averageResponseTime: 0,
        cacheSize: 0,
      };
    }
  }

  async cleanup(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const now = new Date().toISOString();

      // 清理过期条目
      const expiredStmt = this.db.prepare(`
        DELETE FROM cache_entries WHERE expires_at < ?
      `);
      const expiredResult = expiredStmt.run(now);

      // 清理内存缓存中的过期条目
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.expiresAt < new Date()) {
          this.memoryCache.delete(key);
        }
      }

      // 如果缓存仍然超过大小限制，删除最旧的条目
      await this.ensureCacheSize();

      this.logger.info('Cache cleanup completed', { 
        expiredRemoved: expiredResult.changes 
      });
    } catch (error) {
      this.logger.error('Cache cleanup failed', error as Error);
    }
  }

  private async ensureCacheSize(): Promise<void> {
    try {
      const countStmt = this.db.prepare(`SELECT COUNT(*) as count FROM cache_entries`);
      const currentCount = (countStmt.get() as { count: number }).count;

      if (currentCount >= this.config.maxSize) {
        const deleteCount = currentCount - this.config.maxSize + 1;
        
        // 删除最旧的条目（LRU策略）
        const deleteStmt = this.db.prepare(`
          DELETE FROM cache_entries 
          WHERE key IN (
            SELECT key FROM cache_entries 
            ORDER BY last_accessed ASC 
            LIMIT ?
          )
        `);
        
        const result = deleteStmt.run(deleteCount);
        
        this.logger.debug('Cache size limit enforced', { 
          removed: result.changes,
          maxSize: this.config.maxSize 
        });
      }
    } catch (error) {
      this.logger.error('Failed to enforce cache size limit', error as Error);
    }
  }

  private updateAccessStats(key: string): void {
    try {
      const stmt = this.db.prepare(`
        UPDATE cache_entries 
        SET access_count = access_count + 1, last_accessed = ?
        WHERE key = ?
      `);
      
      stmt.run(new Date().toISOString(), key);
    } catch (error) {
      this.logger.debug('Failed to update access stats', { key, error: (error as Error).message });
    }
  }

  private startCleanupScheduler(): void {
    if (!this.config.enabled) {
      return;
    }

    this.cleanupInterval = setInterval(async () => {
      await this.cleanup();
    }, this.config.cleanupInterval);

    this.logger.info('Cache cleanup scheduler started', { 
      interval: this.config.cleanupInterval 
    });
  }

  // 便捷方法：基于URL和选项生成缓存键并获取
  async getByUrl(url: string, options?: any): Promise<CachedContent | null> {
    const key = this.generateKey(url, options);
    return this.get(key);
  }

  // 便捷方法：基于URL和选项生成缓存键并设置
  async setByUrl(url: string, content: StructuredContent, options?: any, ttl?: number): Promise<void> {
    const key = this.generateKey(url, options);
    return this.set(key, content, ttl);
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.db) {
      try {
        this.db.close();
        this.logger.info('Cache manager destroyed');
      } catch (error) {
        this.logger.error('Failed to close cache database', error as Error);
      }
    }

    this.memoryCache.clear();
  }
}
