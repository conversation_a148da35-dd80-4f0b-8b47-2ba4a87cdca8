import { ServerConfig } from '../types/index.js';
import { randomBytes } from 'crypto';
import { existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';

// 确保数据目录存在
const ensureDataDir = (path: string) => {
  const dir = dirname(path);
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true });
  }
};

// 生成默认加密密钥
const generateEncryptionKey = (): string => {
  return randomBytes(32).toString('hex');
};

// 默认配置
export const defaultConfig: ServerConfig = {
  server: {
    port: parseInt(process.env['MCP_PORT'] || '3000'),
    host: process.env['MCP_HOST'] || 'localhost',
    maxConcurrency: parseInt(process.env['MCP_MAX_CONCURRENCY'] || '10'),
    timeout: parseInt(process.env['MCP_TIMEOUT'] || '30000'),
  },
  
  proxy: {
    autoDetect: process.env['MCP_PROXY_AUTO_DETECT'] !== 'false',
    customProxies: [],
    healthCheckInterval: parseInt(process.env['MCP_PROXY_HEALTH_CHECK_INTERVAL'] || '300000'),
    rotationStrategy: (process.env['MCP_PROXY_ROTATION_STRATEGY'] as any) || 'performance',
  },
  
  cache: {
    enabled: process.env['MCP_CACHE_ENABLED'] !== 'false',
    maxSize: parseInt(process.env['MCP_CACHE_MAX_SIZE'] || '1000'),
    defaultTTL: parseInt(process.env['MCP_CACHE_DEFAULT_TTL'] || '3600'),
    cleanupInterval: parseInt(process.env['MCP_CACHE_CLEANUP_INTERVAL'] || '3600000'),
    dbPath: process.env['MCP_CACHE_DB_PATH'] || './data/cache.db',
  },
  
  fetcher: {
    userAgents: [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
    ],
    
    defaultHeaders: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
    },
    
    retryConfig: {
      maxRetries: parseInt(process.env['MCP_RETRY_MAX_RETRIES'] || '3'),
      baseDelay: parseInt(process.env['MCP_RETRY_BASE_DELAY'] || '1000'),
      maxDelay: parseInt(process.env['MCP_RETRY_MAX_DELAY'] || '30000'),
      backoffFactor: parseFloat(process.env['MCP_RETRY_BACKOFF_FACTOR'] || '2'),
    },
    
    rateLimiting: {
      enabled: process.env['MCP_RATE_LIMITING_ENABLED'] !== 'false',
      requestsPerSecond: parseInt(process.env['MCP_RATE_LIMITING_RPS'] || '5'),
      burstSize: parseInt(process.env['MCP_RATE_LIMITING_BURST'] || '10'),
    },
  },
  
  security: {
    encryptionKey: process.env['MCP_ENCRYPTION_KEY'] || generateEncryptionKey(),
    authStorePath: process.env['MCP_AUTH_STORE_PATH'] || './data/auth.db',
    logLevel: (process.env['MCP_LOG_LEVEL'] as any) || 'info',
    maxRequestSize: parseInt(process.env['MCP_MAX_REQUEST_SIZE'] || '10485760'),
  },
};

// 初始化数据目录
ensureDataDir(defaultConfig.cache.dbPath);
ensureDataDir(defaultConfig.security.authStorePath);

// 配置验证和加载函数
export const loadConfig = (): ServerConfig => {
  try {
    // 这里可以添加从配置文件加载的逻辑
    // 例如从 config.json 或 config.yaml 文件加载
    
    return defaultConfig;
  } catch (error) {
    console.warn('Failed to load custom config, using defaults:', error);
    return defaultConfig;
  }
};

// 环境特定配置
export const getEnvironmentConfig = (): Partial<ServerConfig> => {
  const env = process.env['NODE_ENV'] || 'development';
  
  switch (env) {
    case 'production':
      return {
        security: {
          ...defaultConfig.security,
          logLevel: 'warn',
        },
        fetcher: {
          ...defaultConfig.fetcher,
          rateLimiting: {
            enabled: true,
            requestsPerSecond: 10,
            burstSize: 20,
          },
        },
      };
      
    case 'test':
      return {
        cache: {
          ...defaultConfig.cache,
          enabled: false,
        },
        security: {
          ...defaultConfig.security,
          logLevel: 'error',
        },
      };
      
    case 'development':
    default:
      return {
        security: {
          ...defaultConfig.security,
          logLevel: 'debug',
        },
        fetcher: {
          ...defaultConfig.fetcher,
          rateLimiting: {
            enabled: false,
            requestsPerSecond: 100,
            burstSize: 200,
          },
        },
      };
  }
};

// 合并配置
export const createConfig = (): ServerConfig => {
  const base = loadConfig();
  const envOverrides = getEnvironmentConfig();
  
  return {
    ...base,
    ...envOverrides,
    server: { ...base.server, ...envOverrides.server },
    proxy: { ...base.proxy, ...envOverrides.proxy },
    cache: { ...base.cache, ...envOverrides.cache },
    fetcher: { ...base.fetcher, ...envOverrides.fetcher },
    security: { ...base.security, ...envOverrides.security },
  };
};
