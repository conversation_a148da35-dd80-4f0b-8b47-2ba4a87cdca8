#!/usr/bin/env node

import { MCPServer } from './mcp/server.js';
import { createConfig } from './config/default.js';
import { initLogger, getLogger } from './utils/logger.js';
import { ContentHunterError } from './types/index.js';

// 确保在MCP环境中正确运行
process.title = 'mcp-content-hunter';

async function main() {
  try {
    // 加载配置
    const config = createConfig();
    
    // 初始化日志
    initLogger(config);
    const logger = getLogger({ component: 'Main' });
    
    logger.info('Starting MCP Content Hunter Server', {
      version: '1.0.0',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    });

    // 创建并启动服务器
    const server = new MCPServer(config);
    
    // 优雅关闭处理
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      try {
        await server.stop();
        logger.info('Server stopped successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown', error as Error);
        process.exit(1);
      }
    };

    // 注册信号处理器
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', reason as Error, { promise });
      process.exit(1);
    });

    // 启动服务器
    await server.start();
    
    logger.info('MCP Content Hunter Server is running');
    
  } catch (error) {
    const logger = getLogger({ component: 'Main' });
    
    if (error instanceof ContentHunterError) {
      logger.error('Application error', error, { code: error.code });
    } else {
      logger.error('Unexpected error', error as Error);
    }
    
    process.exit(1);
  }
}

// 启动应用
main().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
