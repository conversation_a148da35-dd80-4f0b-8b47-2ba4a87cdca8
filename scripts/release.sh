#!/bin/bash

# MCP Content Hunter Release Script
# Automates the release process with proper checks and validations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're on the main branch
check_branch() {
    local current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        log_error "Must be on main branch to release. Current branch: $current_branch"
        exit 1
    fi
    log_success "On main branch"
}

# Check if working directory is clean
check_clean_working_dir() {
    if [ -n "$(git status --porcelain)" ]; then
        log_error "Working directory is not clean. Please commit or stash changes."
        git status --short
        exit 1
    fi
    log_success "Working directory is clean"
}

# Check if we're up to date with remote
check_remote_sync() {
    git fetch origin main
    local local_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/main)
    
    if [ "$local_commit" != "$remote_commit" ]; then
        log_error "Local branch is not up to date with origin/main"
        log_info "Please run: git pull origin main"
        exit 1
    fi
    log_success "Local branch is up to date with remote"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    if ! npm test; then
        log_error "Tests failed"
        exit 1
    fi
    log_success "All tests passed"
}

# Run linting
run_lint() {
    log_info "Running linter..."
    
    if ! npm run lint; then
        log_error "Linting failed"
        exit 1
    fi
    log_success "Linting passed"
}

# Build project
build_project() {
    log_info "Building project..."
    
    # Clean previous build
    npm run clean
    
    if ! npm run build; then
        log_error "Build failed"
        exit 1
    fi
    log_success "Build completed"
}

# Test MCP server
test_mcp_server() {
    log_info "Testing MCP server..."
    
    if ! npm run test:mcp; then
        log_error "MCP server test failed"
        exit 1
    fi
    log_success "MCP server test passed"
}

# Check npm authentication
check_npm_auth() {
    log_info "Checking NPM authentication..."
    
    if ! npm whoami > /dev/null 2>&1; then
        log_error "Not logged in to NPM. Please run: npm login"
        exit 1
    fi
    
    local npm_user=$(npm whoami)
    log_success "Logged in to NPM as: $npm_user"
}

# Get version bump type
get_version_type() {
    echo ""
    echo "Select version bump type:"
    echo "1) patch (1.0.0 -> 1.0.1) - Bug fixes"
    echo "2) minor (1.0.0 -> 1.1.0) - New features"
    echo "3) major (1.0.0 -> 2.0.0) - Breaking changes"
    echo ""
    
    while true; do
        read -p "Enter choice (1-3): " choice
        case $choice in
            1) echo "patch"; break;;
            2) echo "minor"; break;;
            3) echo "major"; break;;
            *) log_warning "Invalid choice. Please enter 1, 2, or 3.";;
        esac
    done
}

# Update changelog
update_changelog() {
    local new_version=$1
    local version_type=$2
    
    log_info "Updating CHANGELOG.md..."
    
    local date=$(date +%Y-%m-%d)
    local temp_file=$(mktemp)
    
    # Create new changelog entry
    cat > "$temp_file" << EOF
# Changelog

## [$new_version] - $date

### Added
- New features and improvements

### Changed
- Updates and modifications

### Fixed
- Bug fixes and patches

EOF
    
    # Append existing changelog (skip first line if it exists)
    if [ -f CHANGELOG.md ]; then
        tail -n +2 CHANGELOG.md >> "$temp_file"
    fi
    
    mv "$temp_file" CHANGELOG.md
    
    log_success "CHANGELOG.md updated"
}

# Bump version
bump_version() {
    local version_type=$1
    
    log_info "Bumping version ($version_type)..."
    
    local old_version=$(node -p "require('./package.json').version")
    npm version "$version_type" --no-git-tag-version
    local new_version=$(node -p "require('./package.json').version")
    
    log_success "Version bumped: $old_version -> $new_version"
    echo "$new_version"
}

# Create git commit and tag
create_git_tag() {
    local version=$1
    
    log_info "Creating git commit and tag..."
    
    git add package.json CHANGELOG.md
    git commit -m "chore: release v$version"
    git tag -a "v$version" -m "Release v$version"
    
    log_success "Created git tag v$version"
}

# Push to remote
push_to_remote() {
    local version=$1
    
    log_info "Pushing to remote..."
    
    git push origin main
    git push origin "v$version"
    
    log_success "Pushed to remote"
}

# Publish to NPM
publish_to_npm() {
    log_info "Publishing to NPM..."
    
    if ! npm publish; then
        log_error "NPM publish failed"
        exit 1
    fi
    
    log_success "Published to NPM"
}

# Show release summary
show_summary() {
    local version=$1
    
    echo ""
    echo "🎉 Release v$version completed successfully!"
    echo ""
    echo "📦 Package: @our-aicorp/mcp-content-hunter@$version"
    echo "🔗 NPM: https://www.npmjs.com/package/@our-aicorp/mcp-content-hunter"
    echo "🏷️  GitHub: https://github.com/our-aicorp/mcp-content-hunter/releases/tag/v$version"
    echo ""
    echo "Next steps:"
    echo "- GitHub Actions will create a GitHub release"
    echo "- Update documentation if needed"
    echo "- Announce the release"
    echo ""
}

# Main release function
main() {
    echo "🚀 MCP Content Hunter Release Script"
    echo "===================================="
    echo ""
    
    # Pre-release checks
    log_info "Running pre-release checks..."
    check_branch
    check_clean_working_dir
    check_remote_sync
    check_npm_auth
    
    # Quality checks
    log_info "Running quality checks..."
    run_lint
    run_tests
    build_project
    test_mcp_server
    
    # Get version bump type
    local version_type=$(get_version_type)
    
    # Confirmation
    echo ""
    read -p "Proceed with $version_type release? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "Release cancelled"
        exit 0
    fi
    
    # Release process
    log_info "Starting release process..."
    local new_version=$(bump_version "$version_type")
    update_changelog "$new_version" "$version_type"
    create_git_tag "$new_version"
    push_to_remote "$new_version"
    publish_to_npm
    
    # Summary
    show_summary "$new_version"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help]"
        echo ""
        echo "Interactive release script for MCP Content Hunter"
        echo ""
        echo "This script will:"
        echo "1. Run pre-release checks"
        echo "2. Run tests and linting"
        echo "3. Build the project"
        echo "4. Bump version"
        echo "5. Update changelog"
        echo "6. Create git tag"
        echo "7. Push to GitHub"
        echo "8. Publish to NPM"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
