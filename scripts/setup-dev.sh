#!/bin/bash

# MCP Content Hunter Development Setup Script
# Sets up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check Node.js version
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        log_info "Please install Node.js (>= 18): https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        log_error "Node.js version is too old (current: $(node -v), required: >= 18)"
        log_info "Please upgrade Node.js: https://nodejs.org/"
        exit 1
    fi
    
    log_success "Node.js version check passed: $(node -v)"
}

# Check npm version
check_npm() {
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    log_success "npm version: $(npm -v)"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    if ! npm install; then
        log_error "Failed to install dependencies"
        exit 1
    fi
    
    log_success "Dependencies installed"
}

# Setup git hooks
setup_git_hooks() {
    log_info "Setting up git hooks..."
    
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for MCP Content Hunter

echo "Running pre-commit checks..."

# Run linter
if ! npm run lint; then
    echo "❌ Linting failed. Please fix the issues before committing."
    exit 1
fi

# Run tests
if ! npm test; then
    echo "❌ Tests failed. Please fix the issues before committing."
    exit 1
fi

echo "✅ Pre-commit checks passed"
EOF

    chmod +x .git/hooks/pre-commit
    log_success "Git hooks setup completed"
}

# Build project
build_project() {
    log_info "Building project..."
    
    if ! npm run build; then
        log_error "Build failed"
        exit 1
    fi
    
    log_success "Project built successfully"
}

# Test MCP server
test_mcp() {
    log_info "Testing MCP server..."
    
    if ! npm run test:mcp; then
        log_warning "MCP server test failed (this might be expected in development)"
    else
        log_success "MCP server test passed"
    fi
}

# Setup VS Code settings
setup_vscode() {
    if [ ! -d ".vscode" ]; then
        mkdir -p .vscode
    fi
    
    # Create VS Code settings
    cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true,
    "**/.nyc_output": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true
  },
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "npm.enableScriptExplorer": true,
  "jest.autoRun": "off"
}
EOF

    # Create VS Code extensions recommendations
    cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "orta.vscode-jest",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-markdown"
  ]
}
EOF

    # Create VS Code launch configuration
    cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug MCP Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/dist/index.js",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "env": {
        "MCP_LOG_LEVEL": "debug"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand"],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
EOF

    log_success "VS Code configuration created"
}

# Create development environment file
create_env_file() {
    if [ ! -f ".env.development" ]; then
        cat > .env.development << 'EOF'
# Development environment variables for MCP Content Hunter

# Logging
MCP_LOG_LEVEL=debug

# Cache
MCP_CACHE_ENABLED=true
MCP_CACHE_MAX_SIZE=100
MCP_CACHE_DEFAULT_TTL=300

# Proxy
MCP_PROXY_AUTO_DETECT=true

# Development
NODE_ENV=development
MCP_TIMEOUT=10000
MCP_MAX_CONCURRENCY=5

# Security (generate a new key for production)
MCP_ENCRYPTION_KEY=dev-key-32-characters-long-here
EOF
        log_success "Development environment file created"
    else
        log_info "Development environment file already exists"
    fi
}

# Show development tips
show_dev_tips() {
    echo ""
    echo "🎉 Development environment setup completed!"
    echo ""
    echo "📋 Available commands:"
    echo "  npm run dev          - Start development server"
    echo "  npm run build        - Build the project"
    echo "  npm test             - Run tests"
    echo "  npm run test:watch   - Run tests in watch mode"
    echo "  npm run lint         - Run linter"
    echo "  npm run lint:fix     - Fix linting issues"
    echo "  npm run format       - Format code"
    echo "  npm run test:mcp     - Test MCP server"
    echo ""
    echo "🔧 Development tips:"
    echo "  - Use 'npm run dev' for development with auto-reload"
    echo "  - Tests run automatically before commits (git hooks)"
    echo "  - VS Code is configured with recommended extensions"
    echo "  - Check .env.development for environment variables"
    echo ""
    echo "📖 Documentation:"
    echo "  - README.md - Project overview"
    echo "  - CONTRIBUTING.md - Development guidelines"
    echo "  - AI-TOOLS-SETUP.md - Integration guide"
    echo ""
    echo "🚀 Next steps:"
    echo "  1. Open the project in VS Code"
    echo "  2. Install recommended extensions"
    echo "  3. Run 'npm run dev' to start development"
    echo "  4. Make your changes and run tests"
    echo ""
}

# Main setup function
main() {
    echo "🛠️  MCP Content Hunter Development Setup"
    echo "======================================="
    echo ""
    
    log_info "Setting up development environment..."
    
    check_node
    check_npm
    install_dependencies
    build_project
    setup_git_hooks
    setup_vscode
    create_env_file
    test_mcp
    
    show_dev_tips
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help]"
        echo ""
        echo "Development environment setup script for MCP Content Hunter"
        echo ""
        echo "This script will:"
        echo "1. Check Node.js and npm versions"
        echo "2. Install dependencies"
        echo "3. Build the project"
        echo "4. Setup git hooks"
        echo "5. Configure VS Code"
        echo "6. Create development environment file"
        echo "7. Test the setup"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
