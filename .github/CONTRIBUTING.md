# Contributing to MCP Content Hunter

Thank you for your interest in contributing to MCP Content Hunter! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js >= 18.0.0
- npm >= 8.0.0
- Git

### Development Setup

1. **Fork and clone the repository**
```bash
git clone https://github.com/your-username/mcp-content-hunter.git
cd mcp-content-hunter
```

2. **Install dependencies**
```bash
npm install
```

3. **Build the project**
```bash
npm run build
```

4. **Run tests**
```bash
npm test
```

5. **Test MCP server**
```bash
npm run test:mcp
```

## 📋 Development Workflow

### Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Test improvements

### Commit Messages
We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(proxy): add SOCKS5 proxy support
fix(cache): resolve memory leak in cache cleanup
docs(integration): update Claude Desktop setup guide
test(core): add unit tests for content processor
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- content-processor.test.ts

# Run tests with coverage
npm test -- --coverage
```

### Test Structure
- **Unit tests**: `tests/core/` - Test individual modules
- **Integration tests**: `tests/integration/` - Test module interactions
- **Performance tests**: `tests/performance/` - Test performance benchmarks

### Writing Tests
- Use descriptive test names
- Follow the AAA pattern (Arrange, Act, Assert)
- Mock external dependencies
- Aim for high test coverage (>80%)

Example:
```typescript
describe('ContentProcessor', () => {
  describe('extractContent', () => {
    it('should extract title and content from HTML', async () => {
      // Arrange
      const html = '<html><body><h1>Title</h1><p>Content</p></body></html>';
      const processor = new ContentProcessor();
      
      // Act
      const result = await processor.extractContent(html, 'https://example.com');
      
      // Assert
      expect(result.title).toBe('Title');
      expect(result.textContent).toContain('Content');
    });
  });
});
```

## 📝 Code Style

### TypeScript Guidelines
- Use strict TypeScript configuration
- Prefer interfaces over types for object shapes
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

### Formatting
We use Prettier and ESLint for code formatting:

```bash
# Check linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Code Organization
```
src/
├── core/           # Core business logic
├── mcp/            # MCP protocol implementation
├── types/          # Type definitions
├── utils/          # Utility functions
└── config/         # Configuration management
```

## 🔧 Adding New Features

### 1. Planning
- Open an issue to discuss the feature
- Get feedback from maintainers
- Consider backward compatibility

### 2. Implementation
- Create a feature branch
- Write tests first (TDD approach)
- Implement the feature
- Update documentation

### 3. MCP Tool Development
When adding new MCP tools:

```typescript
// 1. Define the tool schema
const toolSchema = {
  name: 'new_tool',
  description: 'Description of what the tool does',
  inputSchema: {
    type: 'object',
    properties: {
      // Define parameters
    },
    required: ['required_param']
  }
};

// 2. Implement the handler
async handleNewTool(args: any) {
  // Validate input
  const validatedArgs = NewToolArgsSchema.parse(args);
  
  // Implement logic
  const result = await this.processNewTool(validatedArgs);
  
  return result;
}

// 3. Add tests
describe('new_tool', () => {
  it('should handle valid input', async () => {
    // Test implementation
  });
});
```

## 🐛 Bug Reports

### Before Reporting
- Search existing issues
- Test with the latest version
- Reproduce with minimal configuration

### Bug Report Template
Use the provided issue template and include:
- Clear description of the bug
- Steps to reproduce
- Expected vs actual behavior
- Environment details
- Error logs
- Configuration (sanitized)

## 📖 Documentation

### Types of Documentation
- **README.md**: Project overview and quick start
- **API Documentation**: Detailed API reference
- **Integration Guides**: AI tool setup instructions
- **Code Comments**: Inline documentation

### Documentation Standards
- Use clear, concise language
- Include code examples
- Keep examples up to date
- Test all code examples

## 🔍 Code Review Process

### Submitting PRs
1. Create a descriptive PR title
2. Fill out the PR template completely
3. Ensure all tests pass
4. Update documentation if needed
5. Request review from maintainers

### Review Criteria
- Code quality and style
- Test coverage
- Documentation updates
- Backward compatibility
- Performance impact
- Security considerations

## 🚀 Release Process

### Version Bumping
We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Workflow
1. Create release PR with version bump
2. Update CHANGELOG.md
3. Merge to main branch
4. GitHub Actions automatically publishes to NPM
5. Create GitHub release with release notes

## 🤝 Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers
- Provide constructive feedback
- Focus on the issue, not the person

### Getting Help
- Check existing documentation
- Search closed issues
- Ask questions in discussions
- Join our community channels

## 📞 Contact

- **Issues**: [GitHub Issues](https://github.com/our-aicorp/mcp-content-hunter/issues)
- **Discussions**: [GitHub Discussions](https://github.com/our-aicorp/mcp-content-hunter/discussions)
- **Email**: <EMAIL>

## 🙏 Recognition

Contributors will be recognized in:
- CHANGELOG.md for their contributions
- README.md contributors section
- GitHub contributors graph

Thank you for contributing to MCP Content Hunter! 🎉
