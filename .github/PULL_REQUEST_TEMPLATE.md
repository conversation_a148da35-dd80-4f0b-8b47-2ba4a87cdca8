# Pull Request

## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Test improvements

## Changes Made
- List the main changes made in this PR
- Include any new dependencies added
- Mention any configuration changes needed

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed
- [ ] MCP server tested with AI tools

### Test Results
```
# Include relevant test output or screenshots
```

## AI Tool Compatibility
Which AI tools have been tested with these changes?
- [ ] Claude Des<PERSON>op
- [ ] Cursor
- [ ] Cline (VS Code)
- [ ] Augment
- [ ] Manual MCP testing

## Breaking Changes
If this is a breaking change, describe:
- What breaks
- Migration path for users
- Updated documentation

## Documentation
- [ ] README updated
- [ ] API documentation updated
- [ ] Integration guides updated
- [ ] Changelog updated

## Checklist
- [ ] Code follows the project's style guidelines
- [ ] Self-review of code completed
- [ ] Code is commented, particularly in hard-to-understand areas
- [ ] Corresponding changes to documentation made
- [ ] No new warnings introduced
- [ ] Tests added that prove the fix is effective or feature works
- [ ] New and existing unit tests pass locally
- [ ] Any dependent changes have been merged and published

## Related Issues
Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Additional Notes
Any additional information that reviewers should know.
