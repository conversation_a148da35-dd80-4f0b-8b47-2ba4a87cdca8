---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Install the package with '...'
2. Configure AI tool with '...'
3. Run command '...'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Error Output**
If applicable, add the error output or logs to help explain your problem.

```
Paste error output here
```

**Environment (please complete the following information):**
 - OS: [e.g. macOS, Windows, Linux]
 - Node.js version: [e.g. 18.17.0]
 - Package version: [e.g. 1.0.0]
 - AI Tool: [e.g. <PERSON>op, Cursor, Cline]
 - AI Tool version: [e.g. 1.2.3]

**Configuration**
Please share your MCP configuration (remove any sensitive information):

```json
{
  "mcpServers": {
    "content-hunter": {
      // Your configuration here
    }
  }
}
```

**Additional context**
Add any other context about the problem here.

**Logs**
If possible, please include relevant logs:
- MCP server logs
- AI tool logs
- Browser console logs (if applicable)

**Checklist**
- [ ] I have searched for existing issues
- [ ] I have tried the latest version
- [ ] I have included all relevant information
- [ ] I have tested with minimal configuration
