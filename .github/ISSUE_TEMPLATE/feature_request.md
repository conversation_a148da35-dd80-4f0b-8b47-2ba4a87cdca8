---
name: Feature request
about: Suggest an idea for this project
title: '[FEATURE] '
labels: 'enhancement'
assignees: ''

---

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Use case**
Describe the specific use case for this feature:
- What AI tool would you use it with?
- What type of content would you extract?
- How would this improve your workflow?

**Implementation suggestions**
If you have ideas about how this could be implemented, please share them:
- API changes needed
- Configuration options
- Technical considerations

**Additional context**
Add any other context, screenshots, or examples about the feature request here.

**Priority**
How important is this feature to you?
- [ ] Critical - blocking my workflow
- [ ] High - would significantly improve my workflow  
- [ ] Medium - nice to have
- [ ] Low - minor improvement

**Compatibility**
Which AI tools should this feature support?
- [ ] <PERSON>
- [ ] Cursor
- [ ] Cline (VS Code)
- [ ] Augment
- [ ] Other (please specify): ___________
