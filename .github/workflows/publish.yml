name: Publish to NPM

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      dry_run:
        description: 'Dry run (do not actually publish)'
        required: false
        default: false
        type: boolean

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        registry-url: 'https://registry.npmjs.org'
        cache: 'npm'
        
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Run linter
      run: npm run lint
      
    - name: Build project
      run: npm run build
      
    - name: Test MCP server
      run: npm run test:mcp
      
    - name: Check if already published
      id: check_published
      run: |
        CURRENT_VERSION=$(node -p "require('./package.json').version")
        NPM_VERSION=$(npm view @our-aicorp/mcp-content-hunter version 2>/dev/null || echo "0.0.0")
        echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        echo "npm_version=$NPM_VERSION" >> $GITHUB_OUTPUT
        
        if [ "$CURRENT_VERSION" = "$NPM_VERSION" ]; then
          echo "needs_version_bump=true" >> $GITHUB_OUTPUT
        else
          echo "needs_version_bump=false" >> $GITHUB_OUTPUT
        fi
        
    - name: Bump version
      if: steps.check_published.outputs.needs_version_bump == 'true'
      run: |
        npm version ${{ github.event.inputs.version_type }} --no-git-tag-version
        NEW_VERSION=$(node -p "require('./package.json').version")
        echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV
        
    - name: Update CHANGELOG
      run: |
        VERSION=$(node -p "require('./package.json').version")
        DATE=$(date +%Y-%m-%d)
        
        # Create new changelog entry
        echo "# Changelog" > CHANGELOG_NEW.md
        echo "" >> CHANGELOG_NEW.md
        echo "## [$VERSION] - $DATE" >> CHANGELOG_NEW.md
        echo "" >> CHANGELOG_NEW.md
        echo "### Added" >> CHANGELOG_NEW.md
        echo "- New features and improvements" >> CHANGELOG_NEW.md
        echo "" >> CHANGELOG_NEW.md
        echo "### Changed" >> CHANGELOG_NEW.md
        echo "- Updates and modifications" >> CHANGELOG_NEW.md
        echo "" >> CHANGELOG_NEW.md
        echo "### Fixed" >> CHANGELOG_NEW.md
        echo "- Bug fixes and patches" >> CHANGELOG_NEW.md
        echo "" >> CHANGELOG_NEW.md
        
        # Append existing changelog if it exists
        if [ -f CHANGELOG.md ]; then
          tail -n +2 CHANGELOG.md >> CHANGELOG_NEW.md
        fi
        
        mv CHANGELOG_NEW.md CHANGELOG.md
        
    - name: Commit version bump
      if: steps.check_published.outputs.needs_version_bump == 'true'
      run: |
        git add package.json CHANGELOG.md
        git commit -m "chore: bump version to v$(node -p "require('./package.json').version")"
        
    - name: Create and push tag
      if: github.event.inputs.dry_run != 'true'
      run: |
        VERSION=$(node -p "require('./package.json').version")
        git tag -a "v$VERSION" -m "Release v$VERSION"
        git push origin main
        git push origin "v$VERSION"
        
    - name: Dry run publish
      if: github.event.inputs.dry_run == 'true'
      run: |
        echo "🔍 Dry run mode - would publish:"
        npm pack --dry-run
        echo "📦 Package contents:"
        npm pack
        tar -tzf *.tgz
        
    - name: Publish to NPM
      if: github.event.inputs.dry_run != 'true'
      run: npm publish
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        
    - name: Create GitHub Release
      if: github.event.inputs.dry_run != 'true'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ env.NEW_VERSION || steps.check_published.outputs.current_version }}
        release_name: Release v${{ env.NEW_VERSION || steps.check_published.outputs.current_version }}
        body: |
          ## 🚀 Release v${{ env.NEW_VERSION || steps.check_published.outputs.current_version }}
          
          ### 📦 Installation
          ```bash
          npm install -g @our-aicorp/mcp-content-hunter
          ```
          
          ### 🔧 Quick Setup
          ```bash
          npx @our-aicorp/mcp-content-hunter --help
          ```
          
          ### 📖 Documentation
          - [Installation Guide](https://github.com/our-aicorp/mcp-content-hunter#installation)
          - [AI Tools Integration](https://github.com/our-aicorp/mcp-content-hunter/blob/main/AI-TOOLS-SETUP.md)
          - [API Documentation](https://github.com/our-aicorp/mcp-content-hunter/blob/main/INTEGRATION.md)
          
          See [CHANGELOG.md](https://github.com/our-aicorp/mcp-content-hunter/blob/main/CHANGELOG.md) for detailed changes.
        draft: false
        prerelease: false
