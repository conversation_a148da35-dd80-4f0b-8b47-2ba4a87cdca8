import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ContentFetcher } from '../../src/core/content-fetcher.js';
import { ProxyManager } from '../../src/core/proxy-manager.js';
import { AuthManager } from '../../src/core/auth-manager.js';
import { CacheManager } from '../../src/core/cache-manager.js';
import { ContentProcessor } from '../../src/core/content-processor.js';
import { createTestConfig, cleanupTestData, mockHtmlContent, delay } from '../utils/test-helpers.js';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock playwright
jest.mock('playwright', () => ({
  chromium: {
    launch: jest.fn(() => Promise.resolve({
      newContext: jest.fn(() => Promise.resolve({
        newPage: jest.fn(() => Promise.resolve({
          goto: jest.fn(),
          content: jest.fn(() => Promise.resolve(mockHtmlContent)),
          close: jest.fn(),
        })),
        close: jest.fn(),
      })),
      close: jest.fn(),
    })),
  },
}));

describe('Performance Tests', () => {
  let contentFetcher: ContentFetcher;
  let proxyManager: ProxyManager;
  let authManager: AuthManager;
  let cacheManager: CacheManager;
  let contentProcessor: ContentProcessor;
  let config: ReturnType<typeof createTestConfig>;

  beforeEach(() => {
    cleanupTestData();
    config = createTestConfig({
      cache: { ...createTestConfig().cache, enabled: true },
    });
    
    proxyManager = new ProxyManager(config.proxy);
    authManager = new AuthManager(config.security);
    cacheManager = new CacheManager(config.cache);
    contentProcessor = new ContentProcessor();
    contentFetcher = new ContentFetcher(
      proxyManager,
      authManager,
      cacheManager,
      contentProcessor,
      {
        userAgents: config.fetcher.userAgents,
        defaultHeaders: config.fetcher.defaultHeaders,
      }
    );

    // Mock successful HTTP responses
    mockedAxios.get.mockResolvedValue({
      status: 200,
      data: mockHtmlContent,
      statusText: 'OK',
      headers: { 'content-type': 'text/html' },
      config: {},
    });

    jest.clearAllMocks();
  });

  afterEach(async () => {
    await contentFetcher.destroy();
    proxyManager.destroy();
    authManager.destroy();
    cacheManager.destroy();
    cleanupTestData();
  });

  describe('Single URL Performance', () => {
    it('should fetch content within acceptable time limits', async () => {
      const url = 'https://example.com/test';
      const startTime = Date.now();
      
      const result = await contentFetcher.fetch(url);
      
      const processingTime = Date.now() - startTime;
      
      expect(result).toBeDefined();
      expect(result.url).toBe(url);
      expect(processingTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(result.processingTime).toBeLessThan(5000); // Internal processing time should be < 5s
    });

    it('should benefit from caching on repeated requests', async () => {
      const url = 'https://example.com/cached-test';
      
      // First request (cache miss)
      const start1 = Date.now();
      const result1 = await contentFetcher.fetch(url);
      const time1 = Date.now() - start1;
      
      // Second request (cache hit)
      const start2 = Date.now();
      const result2 = await contentFetcher.fetch(url);
      const time2 = Date.now() - start2;
      
      expect(result1.title).toBe(result2.title);
      expect(time2).toBeLessThan(time1); // Cache should be faster
      expect(time2).toBeLessThan(100); // Cache access should be very fast
    });

    it('should handle static content faster than dynamic', async () => {
      const url = 'https://example.com/static-vs-dynamic';
      
      // Static rendering
      const start1 = Date.now();
      await contentFetcher.fetch(url, { renderMode: 'static' });
      const staticTime = Date.now() - start1;
      
      // Dynamic rendering
      const start2 = Date.now();
      await contentFetcher.fetch(url, { renderMode: 'dynamic' });
      const dynamicTime = Date.now() - start2;
      
      expect(staticTime).toBeLessThan(dynamicTime);
      expect(staticTime).toBeLessThan(3000); // Static should be < 3s
    });
  });

  describe('Batch Processing Performance', () => {
    it('should handle moderate batch sizes efficiently', async () => {
      const urls = Array.from({ length: 10 }, (_, i) => `https://example.com/batch-${i}`);
      const startTime = Date.now();
      
      const results = await contentFetcher.batchFetch(urls);
      
      const totalTime = Date.now() - startTime;
      const avgTimePerUrl = totalTime / urls.length;
      
      expect(results.length).toBe(urls.length);
      expect(totalTime).toBeLessThan(30000); // Should complete within 30 seconds
      expect(avgTimePerUrl).toBeLessThan(5000); // Average time per URL should be reasonable
    });

    it('should maintain performance with concurrent requests', async () => {
      const batchSize = 5;
      const batches = 3;
      const allPromises: Promise<any>[] = [];
      
      const startTime = Date.now();
      
      // Create multiple concurrent batches
      for (let batch = 0; batch < batches; batch++) {
        const urls = Array.from({ length: batchSize }, (_, i) => 
          `https://example.com/concurrent-${batch}-${i}`
        );
        allPromises.push(contentFetcher.batchFetch(urls));
      }
      
      const allResults = await Promise.all(allPromises);
      const totalTime = Date.now() - startTime;
      
      const totalUrls = batchSize * batches;
      const successfulResults = allResults.flat().length;
      
      expect(successfulResults).toBe(totalUrls);
      expect(totalTime).toBeLessThan(45000); // Should handle concurrency well
    });

    it('should handle large batch sizes without memory issues', async () => {
      const largeUrls = Array.from({ length: 50 }, (_, i) => `https://example.com/large-${i}`);
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      const results = await contentFetcher.batchFetch(largeUrls);
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      expect(results.length).toBe(largeUrls.length);
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // Should not use more than 100MB additional memory
    });
  });

  describe('Cache Performance', () => {
    it('should maintain fast cache access under load', async () => {
      const numEntries = 100;
      const urls = Array.from({ length: numEntries }, (_, i) => `https://example.com/cache-${i}`);
      
      // Populate cache
      for (const url of urls) {
        await contentFetcher.fetch(url);
      }
      
      // Measure cache access performance
      const accessTimes: number[] = [];
      
      for (let i = 0; i < 20; i++) {
        const randomUrl = urls[Math.floor(Math.random() * urls.length)];
        const start = Date.now();
        await contentFetcher.fetch(randomUrl);
        accessTimes.push(Date.now() - start);
      }
      
      const avgAccessTime = accessTimes.reduce((a, b) => a + b, 0) / accessTimes.length;
      const maxAccessTime = Math.max(...accessTimes);
      
      expect(avgAccessTime).toBeLessThan(50); // Average cache access should be < 50ms
      expect(maxAccessTime).toBeLessThan(200); // Max cache access should be < 200ms
    });

    it('should handle cache cleanup efficiently', async () => {
      // Fill cache with many entries
      const numEntries = 200;
      for (let i = 0; i < numEntries; i++) {
        await cacheManager.set(`key-${i}`, {
          url: `https://example.com/${i}`,
          title: `Title ${i}`,
          content: `<p>Content ${i}</p>`,
          markdown: `# Title ${i}\n\nContent ${i}`,
          metadata: { keywords: [] },
          extractedAt: new Date(),
          processingTime: 100,
          qualityScore: 0.8,
          source: 'static',
        });
      }
      
      const startTime = Date.now();
      await cacheManager.cleanup();
      const cleanupTime = Date.now() - startTime;
      
      expect(cleanupTime).toBeLessThan(5000); // Cleanup should complete within 5 seconds
      
      const stats = await cacheManager.getStats();
      expect(stats.totalEntries).toBeLessThanOrEqual(config.cache.maxSize);
    });
  });

  describe('Content Processing Performance', () => {
    it('should process complex HTML efficiently', async () => {
      const complexHtml = `
        <html>
          <body>
            ${Array.from({ length: 100 }, (_, i) => `
              <div class="section-${i}">
                <h2>Section ${i}</h2>
                <p>This is paragraph ${i} with some <a href="https://example.com/${i}">links</a> and <strong>formatting</strong>.</p>
                <ul>
                  ${Array.from({ length: 5 }, (_, j) => `<li>Item ${i}-${j}</li>`).join('')}
                </ul>
                <img src="https://example.com/image-${i}.jpg" alt="Image ${i}">
              </div>
            `).join('')}
          </body>
        </html>
      `;
      
      const startTime = Date.now();
      const extracted = await contentProcessor.extractContent(complexHtml, 'https://example.com');
      const extractionTime = Date.now() - startTime;
      
      const markdownStart = Date.now();
      const markdown = contentProcessor.toMarkdown(extracted);
      const markdownTime = Date.now() - markdownStart;
      
      const qualityStart = Date.now();
      const quality = contentProcessor.assessQuality(extracted);
      const qualityTime = Date.now() - qualityStart;
      
      expect(extractionTime).toBeLessThan(2000); // Extraction should be < 2s
      expect(markdownTime).toBeLessThan(1000); // Markdown conversion should be < 1s
      expect(qualityTime).toBeLessThan(500); // Quality assessment should be < 500ms
      
      expect(extracted.title).toBeDefined();
      expect(markdown.length).toBeGreaterThan(0);
      expect(quality.overall).toBeGreaterThan(0);
    });

    it('should handle multiple processing tasks concurrently', async () => {
      const htmlVariants = Array.from({ length: 10 }, (_, i) => `
        <html>
          <body>
            <h1>Article ${i}</h1>
            <p>Content for article ${i} with some text to process.</p>
          </body>
        </html>
      `);
      
      const startTime = Date.now();
      
      const promises = htmlVariants.map(async (html, i) => {
        const extracted = await contentProcessor.extractContent(html, `https://example.com/${i}`);
        const markdown = contentProcessor.toMarkdown(extracted);
        const quality = contentProcessor.assessQuality(extracted);
        return { extracted, markdown, quality };
      });
      
      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      
      expect(results.length).toBe(htmlVariants.length);
      expect(totalTime).toBeLessThan(5000); // All processing should complete within 5s
      
      results.forEach((result, i) => {
        expect(result.extracted.title).toContain(`Article ${i}`);
        expect(result.markdown).toContain(`# Article ${i}`);
        expect(result.quality.overall).toBeGreaterThan(0);
      });
    });
  });

  describe('Memory Usage', () => {
    it('should maintain stable memory usage during extended operation', async () => {
      const initialMemory = process.memoryUsage();
      
      // Perform many operations
      for (let i = 0; i < 50; i++) {
        await contentFetcher.fetch(`https://example.com/memory-test-${i}`);
        
        // Occasionally force garbage collection if available
        if (i % 10 === 0 && global.gc) {
          global.gc();
        }
      }
      
      const finalMemory = process.memoryUsage();
      const heapIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(heapIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Error Recovery Performance', () => {
    it('should handle failures quickly without blocking other requests', async () => {
      // Mock some requests to fail
      mockedAxios.get
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Timeout'))
        .mockResolvedValue({
          status: 200,
          data: mockHtmlContent,
          statusText: 'OK',
          headers: {},
          config: {},
        });
      
      const urls = [
        'https://example.com/fail-1',
        'https://example.com/fail-2',
        'https://example.com/success-1',
        'https://example.com/success-2',
      ];
      
      const startTime = Date.now();
      const results = await contentFetcher.batchFetch(urls);
      const totalTime = Date.now() - startTime;
      
      // Should complete quickly despite failures
      expect(totalTime).toBeLessThan(15000);
      expect(results.length).toBe(2); // Only successful requests
    });
  });
});
