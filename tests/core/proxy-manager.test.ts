import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ProxyManager } from '../../src/core/proxy-manager.js';
import { createTestConfig, testData, delay } from '../utils/test-helpers.js';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock child_process
jest.mock('child_process', () => ({
  execSync: jest.fn(),
}));

// Mock os
jest.mock('os', () => ({
  platform: jest.fn(() => 'linux'),
}));

describe('ProxyManager', () => {
  let proxyManager: ProxyManager;
  let config: ReturnType<typeof createTestConfig>;

  beforeEach(() => {
    config = createTestConfig();
    proxyManager = new ProxyManager(config.proxy);
    jest.clearAllMocks();
  });

  afterEach(() => {
    proxyManager.destroy();
  });

  describe('constructor', () => {
    it('should initialize with custom proxies', () => {
      const configWithProxies = createTestConfig({
        proxy: {
          ...config.proxy,
          customProxies: [testData.proxies[0]],
        },
      });

      const manager = new ProxyManager(configWithProxies.proxy);
      const stats = manager.getProxyStats();
      
      expect(stats.total).toBe(1);
      expect(stats.enabled).toBe(1);
      
      manager.destroy();
    });

    it('should auto-detect system proxy when enabled', async () => {
      const configWithAutoDetect = createTestConfig({
        proxy: {
          ...config.proxy,
          autoDetect: true,
        },
      });

      // Mock environment variable
      process.env.http_proxy = 'http://proxy.example.com:8080';

      const manager = new ProxyManager(configWithAutoDetect.proxy);
      
      // Wait a bit for async initialization
      await delay(100);
      
      const stats = manager.getProxyStats();
      expect(stats.total).toBeGreaterThanOrEqual(0);
      
      delete process.env.http_proxy;
      manager.destroy();
    });
  });

  describe('addProxy', () => {
    it('should add a proxy successfully', () => {
      const proxy = testData.proxies[0];
      proxyManager.addProxy(proxy);
      
      const stats = proxyManager.getProxyStats();
      expect(stats.total).toBe(1);
      expect(stats.enabled).toBe(1);
    });

    it('should replace existing proxy with same ID', () => {
      const proxy1 = { ...testData.proxies[0], port: 8080 };
      const proxy2 = { ...testData.proxies[0], port: 8081 };
      
      proxyManager.addProxy(proxy1);
      proxyManager.addProxy(proxy2);
      
      const stats = proxyManager.getProxyStats();
      expect(stats.total).toBe(1); // Should replace, not add
    });
  });

  describe('removeProxy', () => {
    it('should remove a proxy successfully', () => {
      const proxy = testData.proxies[0];
      proxyManager.addProxy(proxy);
      
      let stats = proxyManager.getProxyStats();
      expect(stats.total).toBe(1);
      
      proxyManager.removeProxy(proxy.id);
      
      stats = proxyManager.getProxyStats();
      expect(stats.total).toBe(0);
    });

    it('should handle removing non-existent proxy', () => {
      proxyManager.removeProxy('non-existent');
      
      const stats = proxyManager.getProxyStats();
      expect(stats.total).toBe(0);
    });
  });

  describe('getHealthyProxy', () => {
    beforeEach(() => {
      testData.proxies.forEach(proxy => proxyManager.addProxy(proxy));
    });

    it('should return healthy proxy with round-robin strategy', async () => {
      const configRoundRobin = createTestConfig({
        proxy: {
          ...config.proxy,
          rotationStrategy: 'round-robin',
        },
      });
      
      const manager = new ProxyManager(configRoundRobin.proxy);
      testData.proxies.forEach(proxy => {
        if (proxy.isHealthy) manager.addProxy(proxy);
      });

      const proxy1 = await manager.getHealthyProxy();
      const proxy2 = await manager.getHealthyProxy();
      
      expect(proxy1).toBeDefined();
      expect(proxy2).toBeDefined();
      
      manager.destroy();
    });

    it('should return random proxy with random strategy', async () => {
      const configRandom = createTestConfig({
        proxy: {
          ...config.proxy,
          rotationStrategy: 'random',
        },
      });
      
      const manager = new ProxyManager(configRandom.proxy);
      testData.proxies.forEach(proxy => {
        if (proxy.isHealthy) manager.addProxy(proxy);
      });

      const proxy = await manager.getHealthyProxy();
      expect(proxy).toBeDefined();
      
      manager.destroy();
    });

    it('should return performance-based proxy', async () => {
      const configPerformance = createTestConfig({
        proxy: {
          ...config.proxy,
          rotationStrategy: 'performance',
        },
      });
      
      const manager = new ProxyManager(configPerformance.proxy);
      
      const fastProxy = { ...testData.proxies[0], responseTime: 100 };
      const slowProxy = { ...testData.proxies[1], responseTime: 500, isHealthy: true };
      
      manager.addProxy(fastProxy);
      manager.addProxy(slowProxy);

      const proxy = await manager.getHealthyProxy();
      expect(proxy?.id).toBe(fastProxy.id);
      
      manager.destroy();
    });

    it('should return null when no healthy proxies available', async () => {
      // Add only unhealthy proxies
      const unhealthyProxy = { ...testData.proxies[0], isHealthy: false };
      proxyManager.addProxy(unhealthyProxy);

      const proxy = await proxyManager.getHealthyProxy();
      expect(proxy).toBeNull();
    });
  });

  describe('checkProxyHealth', () => {
    it('should mark proxy as healthy on successful check', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { ip: '*******' },
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const proxy = { ...testData.proxies[0], isHealthy: false };
      const isHealthy = await proxyManager.checkProxyHealth(proxy);
      
      expect(isHealthy).toBe(true);
      expect(proxy.isHealthy).toBe(true);
      expect(proxy.responseTime).toBeGreaterThan(0);
      expect(proxy.lastChecked).toBeInstanceOf(Date);
    });

    it('should mark proxy as unhealthy on failed check', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection failed'));

      const proxy = { ...testData.proxies[0], isHealthy: true };
      const isHealthy = await proxyManager.checkProxyHealth(proxy);
      
      expect(isHealthy).toBe(false);
      expect(proxy.isHealthy).toBe(false);
      expect(proxy.lastChecked).toBeInstanceOf(Date);
    });

    it('should handle timeout errors', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('timeout'));

      const proxy = testData.proxies[0];
      const isHealthy = await proxyManager.checkProxyHealth(proxy);
      
      expect(isHealthy).toBe(false);
    });
  });

  describe('detectSystemProxy', () => {
    it('should detect Linux proxy from environment', async () => {
      process.env.http_proxy = 'http://proxy.example.com:8080';
      
      const proxy = await proxyManager.detectSystemProxy();
      
      expect(proxy).toBeDefined();
      expect(proxy?.host).toBe('proxy.example.com');
      expect(proxy?.port).toBe(8080);
      expect(proxy?.type).toBe('http');
      
      delete process.env.http_proxy;
    });

    it('should detect proxy with authentication', async () => {
      process.env.https_proxy = 'https://user:<EMAIL>:8080';
      
      const proxy = await proxyManager.detectSystemProxy();
      
      expect(proxy).toBeDefined();
      expect(proxy?.username).toBe('user');
      expect(proxy?.password).toBe('pass');
      
      delete process.env.https_proxy;
    });

    it('should return null when no proxy detected', async () => {
      delete process.env.http_proxy;
      delete process.env.https_proxy;
      delete process.env.HTTP_PROXY;
      delete process.env.HTTPS_PROXY;
      
      const proxy = await proxyManager.detectSystemProxy();
      expect(proxy).toBeNull();
    });
  });

  describe('toAxiosProxy', () => {
    it('should convert proxy config to axios format', () => {
      const proxy = testData.proxies[1]; // Has username/password
      const axiosProxy = proxyManager.toAxiosProxy(proxy);
      
      expect(axiosProxy.protocol).toBe(proxy.type);
      expect(axiosProxy.host).toBe(proxy.host);
      expect(axiosProxy.port).toBe(proxy.port);
      expect(axiosProxy.auth?.username).toBe(proxy.username);
      expect(axiosProxy.auth?.password).toBe(proxy.password);
    });

    it('should handle proxy without authentication', () => {
      const proxy = testData.proxies[0]; // No username/password
      const axiosProxy = proxyManager.toAxiosProxy(proxy);
      
      expect(axiosProxy.auth).toBeUndefined();
    });
  });

  describe('health monitoring', () => {
    it('should start health monitoring', () => {
      const spy = jest.spyOn(global, 'setInterval');
      
      proxyManager.startHealthMonitoring();
      
      expect(spy).toHaveBeenCalled();
      
      spy.mockRestore();
    });

    it('should stop health monitoring', () => {
      const spy = jest.spyOn(global, 'clearInterval');
      
      proxyManager.startHealthMonitoring();
      proxyManager.stopHealthMonitoring();
      
      expect(spy).toHaveBeenCalled();
      
      spy.mockRestore();
    });
  });

  describe('getProxyStats', () => {
    it('should return correct statistics', () => {
      proxyManager.addProxy(testData.proxies[0]); // healthy
      proxyManager.addProxy({ ...testData.proxies[1], isHealthy: false }); // unhealthy
      proxyManager.addProxy({ ...testData.proxies[0], id: 'disabled', enabled: false }); // disabled

      const stats = proxyManager.getProxyStats();
      
      expect(stats.total).toBe(3);
      expect(stats.healthy).toBe(1);
      expect(stats.enabled).toBe(2);
    });
  });

  describe('error handling', () => {
    it('should handle invalid proxy URLs gracefully', async () => {
      const invalidProxy = {
        id: 'invalid',
        type: 'http' as const,
        host: '',
        port: 0,
        enabled: true,
        priority: 1,
        isHealthy: true,
      };

      const isHealthy = await proxyManager.checkProxyHealth(invalidProxy);
      expect(isHealthy).toBe(false);
    });

    it('should handle network errors during health check', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const proxy = testData.proxies[0];
      const isHealthy = await proxyManager.checkProxyHealth(proxy);
      
      expect(isHealthy).toBe(false);
      expect(proxy.isHealthy).toBe(false);
    });
  });
});
