import { describe, it, expect, beforeEach } from '@jest/globals';
import { ContentProcessor } from '../../src/core/content-processor.js';
import { mockHtmlContent, mockSimpleHtml, mockComplexHtml } from '../utils/test-helpers.js';

describe('ContentProcessor', () => {
  let processor: ContentProcessor;

  beforeEach(() => {
    processor = new ContentProcessor();
  });

  describe('extractContent', () => {
    it('should extract content from simple HTML', async () => {
      const result = await processor.extractContent(mockSimpleHtml, 'https://example.com');
      
      expect(result.title).toBe('Simple Title');
      expect(result.textContent).toContain('Simple content paragraph');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should extract content from complex HTML', async () => {
      const result = await processor.extractContent(mockComplexHtml, 'https://example.com');
      
      expect(result.title).toBe('Complex Article Title');
      expect(result.textContent).toContain('First section content');
      expect(result.textContent).toContain('Second section');
      expect(result.byline).toContain('Complex Author');
      expect(result.images).toHaveLength(2);
      expect(result.images[0]).toBe('https://example.com/image1.jpg');
      expect(result.links).toHaveLength(1);
      expect(result.links[0].href).toBe('https://example.com');
    });

    it('should extract metadata correctly', async () => {
      const result = await processor.extractContent(mockHtmlContent, 'https://example.com');
      
      expect(result.title).toBe('Test Article Title');
      expect(result.byline).toContain('Test Author');
      expect(result.images).toHaveLength(1);
      expect(result.links).toHaveLength(1);
    });

    it('should handle empty or invalid HTML', async () => {
      await expect(processor.extractContent('', 'https://example.com'))
        .rejects.toThrow('Failed to extract readable content');
      
      await expect(processor.extractContent('<html></html>', 'https://example.com'))
        .rejects.toThrow('Failed to extract readable content');
    });

    it('should extract images with various src attributes', async () => {
      const htmlWithImages = `
        <html><body>
          <img src="https://example.com/image1.jpg" alt="Image 1">
          <img data-src="https://example.com/image2.png" alt="Image 2">
          <img src="invalid-url" alt="Invalid">
          <img src="https://example.com/not-image.txt" alt="Not Image">
        </body></html>
      `;
      
      const result = await processor.extractContent(htmlWithImages, 'https://example.com');
      expect(result.images).toHaveLength(1); // Only valid image URLs
      expect(result.images[0]).toBe('https://example.com/image1.jpg');
    });
  });

  describe('toMarkdown', () => {
    it('should convert HTML to Markdown', async () => {
      const extracted = await processor.extractContent(mockComplexHtml, 'https://example.com');
      const markdown = processor.toMarkdown(extracted);
      
      expect(markdown).toContain('# Complex Article Title');
      expect(markdown).toContain('## Section 1');
      expect(markdown).toContain('**bold text**');
      expect(markdown).toContain('_italic text_');
      expect(markdown).toContain('[link](https://example.com)');
      expect(markdown).toContain('- Item 1');
      expect(markdown).toContain('`inline code`');
    });

    it('should handle tables in Markdown conversion', async () => {
      const htmlWithTable = `
        <html><body>
          <table>
            <tr><th>Header 1</th><th>Header 2</th></tr>
            <tr><td>Data 1</td><td>Data 2</td></tr>
          </table>
        </body></html>
      `;
      
      const extracted = await processor.extractContent(htmlWithTable, 'https://example.com');
      const markdown = processor.toMarkdown(extracted);
      
      expect(markdown).toContain('| Header 1 | Header 2 |');
      expect(markdown).toContain('| --- | --- |');
      expect(markdown).toContain('| Data 1 | Data 2 |');
    });

    it('should handle code blocks', async () => {
      const htmlWithCode = `
        <html><body>
          <pre><code class="language-javascript">
function test() {
  return "hello";
}
          </code></pre>
        </body></html>
      `;
      
      const extracted = await processor.extractContent(htmlWithCode, 'https://example.com');
      const markdown = processor.toMarkdown(extracted);
      
      expect(markdown).toContain('```javascript');
      expect(markdown).toContain('function test()');
      expect(markdown).toContain('```');
    });
  });

  describe('toJSON', () => {
    it('should convert extracted content to structured JSON', async () => {
      const extracted = await processor.extractContent(mockComplexHtml, 'https://example.com');
      const structured = processor.toJSON(extracted);
      
      expect(structured.title).toBe('Complex Article Title');
      expect(structured.content).toBe(extracted.content);
      expect(structured.markdown).toBeDefined();
      expect(structured.metadata.author).toBe('Complex Author');
      expect(structured.metadata.keywords).toBeInstanceOf(Array);
      expect(structured.extractedAt).toBeInstanceOf(Date);
      expect(structured.qualityScore).toBeGreaterThan(0);
      expect(structured.qualityScore).toBeLessThanOrEqual(1);
    });

    it('should extract keywords from content', async () => {
      const htmlWithKeywords = `
        <html><body>
          <p>JavaScript programming language development coding software engineering technology</p>
        </body></html>
      `;
      
      const extracted = await processor.extractContent(htmlWithKeywords, 'https://example.com');
      const structured = processor.toJSON(extracted);
      
      expect(structured.metadata.keywords).toContain('javascript');
      expect(structured.metadata.keywords).toContain('programming');
      expect(structured.metadata.keywords.length).toBeGreaterThan(0);
    });
  });

  describe('assessQuality', () => {
    it('should assess content quality correctly', async () => {
      const extracted = await processor.extractContent(mockComplexHtml, 'https://example.com');
      const quality = processor.assessQuality(extracted);
      
      expect(quality.overall).toBeGreaterThan(0);
      expect(quality.overall).toBeLessThanOrEqual(1);
      expect(quality.contentLength).toBeGreaterThan(0);
      expect(quality.readability).toBeGreaterThan(0);
      expect(quality.structure).toBeGreaterThan(0);
      expect(quality.metadata).toBeGreaterThan(0);
    });

    it('should give higher scores to better content', async () => {
      const simpleExtracted = await processor.extractContent(mockSimpleHtml, 'https://example.com');
      const complexExtracted = await processor.extractContent(mockComplexHtml, 'https://example.com');
      
      const simpleQuality = processor.assessQuality(simpleExtracted);
      const complexQuality = processor.assessQuality(complexExtracted);
      
      expect(complexQuality.overall).toBeGreaterThan(simpleQuality.overall);
      expect(complexQuality.structure).toBeGreaterThan(simpleQuality.structure);
      expect(complexQuality.metadata).toBeGreaterThan(simpleQuality.metadata);
    });

    it('should handle minimal content', async () => {
      const minimalHtml = '<html><body><p>Short</p></body></html>';
      const extracted = await processor.extractContent(minimalHtml, 'https://example.com');
      const quality = processor.assessQuality(extracted);
      
      expect(quality.overall).toBeGreaterThan(0);
      expect(quality.contentLength).toBeLessThan(0.5); // Short content
    });
  });

  describe('sanitizeHtml', () => {
    it('should remove scripts and styles', () => {
      const dirtyHtml = `
        <html>
          <head>
            <style>body { color: red; }</style>
            <script>alert('xss');</script>
          </head>
          <body>
            <p>Clean content</p>
            <script>console.log('another script');</script>
          </body>
        </html>
      `;
      
      const cleaned = processor.sanitizeHtml(dirtyHtml);
      
      expect(cleaned).not.toContain('<script>');
      expect(cleaned).not.toContain('<style>');
      expect(cleaned).toContain('Clean content');
    });

    it('should handle malformed HTML gracefully', () => {
      const malformedHtml = '<div><p>Unclosed tags<span>content';
      const cleaned = processor.sanitizeHtml(malformedHtml);
      
      expect(cleaned).toContain('content');
      expect(typeof cleaned).toBe('string');
    });
  });

  describe('error handling', () => {
    it('should handle extraction errors gracefully', async () => {
      const invalidHtml = null as any;
      
      await expect(processor.extractContent(invalidHtml, 'https://example.com'))
        .rejects.toThrow();
    });

    it('should fallback to text content when markdown conversion fails', async () => {
      const extracted = await processor.extractContent(mockSimpleHtml, 'https://example.com');
      
      // Mock turndown service to throw error
      const originalTurndown = (processor as any).turndownService.turndown;
      (processor as any).turndownService.turndown = () => {
        throw new Error('Conversion failed');
      };
      
      const markdown = processor.toMarkdown(extracted);
      expect(markdown).toBe(extracted.textContent);
      
      // Restore original method
      (processor as any).turndownService.turndown = originalTurndown;
    });
  });
});
