import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { CacheManager } from '../../src/core/cache-manager.js';
import { createTestConfig, cleanupTestData, delay } from '../utils/test-helpers.js';
import { StructuredContent } from '../../src/types/index.js';

describe('CacheManager', () => {
  let cacheManager: CacheManager;
  let config: ReturnType<typeof createTestConfig>;

  const mockContent: StructuredContent = {
    url: 'https://example.com',
    title: 'Test Article',
    content: '<p>Test content</p>',
    markdown: '# Test Article\n\nTest content',
    metadata: {
      author: 'Test Author',
      description: 'Test description',
      keywords: ['test', 'article'],
    },
    extractedAt: new Date(),
    processingTime: 1000,
    qualityScore: 0.8,
    source: 'static',
  };

  beforeEach(() => {
    cleanupTestData();
    config = createTestConfig({
      cache: {
        enabled: true,
        maxSize: 10,
        defaultTTL: 3600,
        cleanupInterval: 60000,
        dbPath: './test-data/test-cache.db',
      },
    });
    cacheManager = new CacheManager(config.cache);
  });

  afterEach(() => {
    cacheManager.destroy();
    cleanupTestData();
  });

  describe('constructor', () => {
    it('should initialize with cache enabled', () => {
      expect(cacheManager).toBeDefined();
    });

    it('should handle disabled cache', () => {
      const disabledConfig = createTestConfig({
        cache: { ...config.cache, enabled: false },
      });
      
      const disabledManager = new CacheManager(disabledConfig.cache);
      expect(disabledManager).toBeDefined();
      disabledManager.destroy();
    });
  });

  describe('set and get', () => {
    it('should store and retrieve content', async () => {
      const key = 'test-key';
      
      await cacheManager.set(key, mockContent);
      const retrieved = await cacheManager.get(key);
      
      expect(retrieved).toBeDefined();
      expect(retrieved?.content.title).toBe(mockContent.title);
      expect(retrieved?.content.url).toBe(mockContent.url);
    });

    it('should return null for non-existent key', async () => {
      const retrieved = await cacheManager.get('non-existent');
      expect(retrieved).toBeNull();
    });

    it('should handle custom TTL', async () => {
      const key = 'test-ttl';
      const shortTTL = 1; // 1 second
      
      await cacheManager.set(key, mockContent, shortTTL);
      
      // Should exist immediately
      let retrieved = await cacheManager.get(key);
      expect(retrieved).toBeDefined();
      
      // Wait for expiration
      await delay(1100);
      
      // Should be expired
      retrieved = await cacheManager.get(key);
      expect(retrieved).toBeNull();
    });

    it('should update access count and last accessed time', async () => {
      const key = 'test-access';
      
      await cacheManager.set(key, mockContent);
      
      // First access
      const first = await cacheManager.get(key);
      expect(first?.accessCount).toBe(0); // Initial access count
      
      // Second access (after a small delay to ensure different timestamp)
      await delay(10);
      const second = await cacheManager.get(key);
      expect(second?.accessCount).toBeGreaterThan(first?.accessCount || 0);
    });
  });

  describe('delete', () => {
    it('should delete existing entry', async () => {
      const key = 'test-delete';
      
      await cacheManager.set(key, mockContent);
      
      let retrieved = await cacheManager.get(key);
      expect(retrieved).toBeDefined();
      
      await cacheManager.delete(key);
      
      retrieved = await cacheManager.get(key);
      expect(retrieved).toBeNull();
    });

    it('should handle deleting non-existent entry', async () => {
      await expect(cacheManager.delete('non-existent')).resolves.not.toThrow();
    });
  });

  describe('clear', () => {
    it('should clear all entries', async () => {
      await cacheManager.set('key1', mockContent);
      await cacheManager.set('key2', mockContent);
      
      let stats = await cacheManager.getStats();
      expect(stats.totalEntries).toBe(2);
      
      await cacheManager.clear();
      
      stats = await cacheManager.getStats();
      expect(stats.totalEntries).toBe(0);
    });
  });

  describe('cleanup', () => {
    it('should remove expired entries', async () => {
      const shortTTL = 1; // 1 second
      
      await cacheManager.set('expired', mockContent, shortTTL);
      await cacheManager.set('valid', mockContent, 3600);
      
      // Wait for first entry to expire
      await delay(1100);
      
      await cacheManager.cleanup();
      
      const expired = await cacheManager.get('expired');
      const valid = await cacheManager.get('valid');
      
      expect(expired).toBeNull();
      expect(valid).toBeDefined();
    });

    it('should enforce cache size limit', async () => {
      // Fill cache beyond limit
      for (let i = 0; i < 15; i++) {
        await cacheManager.set(`key${i}`, mockContent);
        await delay(10); // Ensure different timestamps
      }
      
      const stats = await cacheManager.getStats();
      expect(stats.totalEntries).toBeLessThanOrEqual(config.cache.maxSize);
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', async () => {
      await cacheManager.set('key1', mockContent);
      await cacheManager.set('key2', mockContent);
      
      // Access one entry to increase hit count
      await cacheManager.get('key1');
      
      const stats = await cacheManager.getStats();
      
      expect(stats.totalEntries).toBe(2);
      expect(stats.cacheSize).toBeGreaterThan(0);
      expect(stats.totalHits).toBeGreaterThan(0);
      expect(stats.hitRate).toBeGreaterThan(0);
    });

    it('should handle empty cache', async () => {
      const stats = await cacheManager.getStats();
      
      expect(stats.totalEntries).toBe(0);
      expect(stats.cacheSize).toBe(0);
      expect(stats.totalHits).toBe(0);
    });
  });

  describe('convenience methods', () => {
    it('should work with getByUrl and setByUrl', async () => {
      const url = 'https://example.com/test';
      const options = { useProxy: true };
      
      await cacheManager.setByUrl(url, mockContent, options);
      const retrieved = await cacheManager.getByUrl(url, options);
      
      expect(retrieved).toBeDefined();
      expect(retrieved?.content.title).toBe(mockContent.title);
    });

    it('should generate different keys for different options', async () => {
      const url = 'https://example.com/test';
      const options1 = { useProxy: true };
      const options2 = { useProxy: false };
      
      await cacheManager.setByUrl(url, mockContent, options1);
      await cacheManager.setByUrl(url, { ...mockContent, title: 'Different' }, options2);
      
      const retrieved1 = await cacheManager.getByUrl(url, options1);
      const retrieved2 = await cacheManager.getByUrl(url, options2);
      
      expect(retrieved1?.content.title).toBe(mockContent.title);
      expect(retrieved2?.content.title).toBe('Different');
    });
  });

  describe('memory cache', () => {
    it('should use memory cache for faster access', async () => {
      const key = 'memory-test';
      
      await cacheManager.set(key, mockContent);
      
      // First access (from disk)
      const start1 = Date.now();
      await cacheManager.get(key);
      const time1 = Date.now() - start1;
      
      // Second access (from memory)
      const start2 = Date.now();
      await cacheManager.get(key);
      const time2 = Date.now() - start2;
      
      // Memory access should be faster (though this might be flaky in tests)
      expect(time2).toBeLessThanOrEqual(time1 + 5); // Allow some margin
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Close the database to simulate error
      cacheManager.destroy();
      
      // Operations should not throw
      await expect(cacheManager.get('test')).resolves.toBeNull();
      await expect(cacheManager.set('test', mockContent)).resolves.not.toThrow();
      await expect(cacheManager.delete('test')).resolves.not.toThrow();
    });

    it('should handle invalid content gracefully', async () => {
      const invalidContent = null as any;
      
      await expect(cacheManager.set('invalid', invalidContent)).resolves.not.toThrow();
    });
  });

  describe('disabled cache', () => {
    let disabledManager: CacheManager;

    beforeEach(() => {
      const disabledConfig = createTestConfig({
        cache: { ...config.cache, enabled: false },
      });
      disabledManager = new CacheManager(disabledConfig.cache);
    });

    afterEach(() => {
      disabledManager.destroy();
    });

    it('should return null for all get operations when disabled', async () => {
      const result = await disabledManager.get('any-key');
      expect(result).toBeNull();
    });

    it('should not store anything when disabled', async () => {
      await disabledManager.set('test', mockContent);
      const result = await disabledManager.get('test');
      expect(result).toBeNull();
    });

    it('should return empty stats when disabled', async () => {
      const stats = await disabledManager.getStats();
      expect(stats.totalEntries).toBe(0);
      expect(stats.hitRate).toBe(0);
    });
  });
});
