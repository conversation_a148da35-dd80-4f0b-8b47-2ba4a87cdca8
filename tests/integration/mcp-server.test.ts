import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { MCPContentHunterServer } from '../../src/mcp/server.js';
import { createTestConfig, cleanupTestData, mockHtmlContent, delay } from '../utils/test-helpers.js';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock playwright
jest.mock('playwright', () => ({
  chromium: {
    launch: jest.fn(() => Promise.resolve({
      newContext: jest.fn(() => Promise.resolve({
        newPage: jest.fn(() => Promise.resolve({
          goto: jest.fn(),
          content: jest.fn(() => Promise.resolve(mockHtmlContent)),
          close: jest.fn(),
          waitForSelector: jest.fn(),
        })),
        close: jest.fn(),
        setExtraHTTPHeaders: jest.fn(),
      })),
      close: jest.fn(),
    })),
  },
}));

describe('MCPContentHunterServer Integration', () => {
  let server: MCPContentHunterServer;
  let config: ReturnType<typeof createTestConfig>;

  beforeEach(() => {
    cleanupTestData();
    config = createTestConfig();
    server = new MCPContentHunterServer(config);
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await server.stop();
    cleanupTestData();
  });

  describe('fetch_content tool', () => {
    beforeEach(() => {
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: mockHtmlContent,
        statusText: 'OK',
        headers: { 'content-type': 'text/html' },
        config: {},
      });
    });

    it('should fetch and process content successfully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'fetch_content',
          arguments: {
            url: 'https://example.com/article',
            options: {
              renderMode: 'static',
              timeout: 10000,
            },
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content).toBeDefined();
      expect(response.content[0].type).toBe('text');
      
      const result = JSON.parse(response.content[0].text);
      expect(result.url).toBe('https://example.com/article');
      expect(result.title).toBe('Test Article Title');
      expect(result.source).toBe('static');
      expect(result.qualityScore).toBeGreaterThan(0);
    });

    it('should handle dynamic rendering', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'fetch_content',
          arguments: {
            url: 'https://spa-app.com/article',
            options: {
              renderMode: 'dynamic',
              waitForSelector: '.content',
            },
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content).toBeDefined();
      const result = JSON.parse(response.content[0].text);
      expect(result.source).toBe('dynamic');
    });

    it('should handle fetch errors gracefully', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const request = {
        method: 'tools/call',
        params: {
          name: 'fetch_content',
          arguments: {
            url: 'https://example.com/error',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
    });

    it('should validate URL format', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'fetch_content',
          arguments: {
            url: 'invalid-url',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
    });
  });

  describe('batch_fetch_content tool', () => {
    beforeEach(() => {
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: mockHtmlContent,
        statusText: 'OK',
        headers: { 'content-type': 'text/html' },
        config: {},
      });
    });

    it('should fetch multiple URLs successfully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'batch_fetch_content',
          arguments: {
            urls: [
              'https://example.com/article1',
              'https://example.com/article2',
            ],
            options: {
              renderMode: 'static',
            },
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content).toBeDefined();
      const results = JSON.parse(response.content[0].text);
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(2);
      expect(results[0].url).toBe('https://example.com/article1');
      expect(results[1].url).toBe('https://example.com/article2');
    });

    it('should handle partial failures in batch', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          data: mockHtmlContent,
          statusText: 'OK',
          headers: {},
          config: {},
        })
        .mockRejectedValueOnce(new Error('Network error'));

      const request = {
        method: 'tools/call',
        params: {
          name: 'batch_fetch_content',
          arguments: {
            urls: [
              'https://example.com/success',
              'https://example.com/error',
            ],
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      const results = JSON.parse(response.content[0].text);
      expect(results.length).toBe(1); // Only successful results
      expect(results[0].url).toBe('https://example.com/success');
    });

    it('should validate URLs array', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'batch_fetch_content',
          arguments: {
            urls: 'not-an-array',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
      expect(response.content[0].text).toContain('must be an array');
    });
  });

  describe('manage_proxy tool', () => {
    it('should add proxy successfully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_proxy',
          arguments: {
            action: 'add',
            proxy: {
              id: 'test-proxy',
              type: 'http',
              host: 'proxy.example.com',
              port: 8080,
              enabled: true,
              priority: 1,
              isHealthy: true,
            },
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('added successfully');
    });

    it('should list proxy statistics', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_proxy',
          arguments: {
            action: 'list',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      const stats = JSON.parse(response.content[0].text);
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('healthy');
      expect(stats).toHaveProperty('enabled');
    });

    it('should remove proxy successfully', async () => {
      // First add a proxy
      await (server as any).server.request({
        method: 'tools/call',
        params: {
          name: 'manage_proxy',
          arguments: {
            action: 'add',
            proxy: {
              id: 'test-proxy',
              type: 'http',
              host: 'proxy.example.com',
              port: 8080,
              enabled: true,
              priority: 1,
              isHealthy: true,
            },
          },
        },
      });

      // Then remove it
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_proxy',
          arguments: {
            action: 'remove',
            proxyId: 'test-proxy',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('removed successfully');
    });

    it('should handle invalid proxy action', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_proxy',
          arguments: {
            action: 'invalid-action',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
      expect(response.content[0].text).toContain('Unknown proxy action');
    });
  });

  describe('manage_auth tool', () => {
    it('should set auth configuration', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_auth',
          arguments: {
            action: 'set',
            domain: 'example.com',
            authConfig: {
              type: 'basic',
              credentials: {
                username: 'testuser',
                password: 'testpass',
              },
            },
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Auth configuration set');
    });

    it('should get auth configuration', async () => {
      // First set auth
      await (server as any).server.request({
        method: 'tools/call',
        params: {
          name: 'manage_auth',
          arguments: {
            action: 'set',
            domain: 'example.com',
            authConfig: {
              type: 'basic',
              credentials: {
                username: 'testuser',
                password: 'testpass',
              },
            },
          },
        },
      });

      // Then get it
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_auth',
          arguments: {
            action: 'get',
            domain: 'example.com',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      const auth = JSON.parse(response.content[0].text);
      expect(auth.type).toBe('basic');
      expect(auth.domain).toBe('example.com');
    });

    it('should list auth domains', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'manage_auth',
          arguments: {
            action: 'list',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      const domains = JSON.parse(response.content[0].text);
      expect(Array.isArray(domains)).toBe(true);
    });
  });

  describe('cache_management tool', () => {
    it('should return cache statistics', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'cache_management',
          arguments: {
            action: 'stats',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      const stats = JSON.parse(response.content[0].text);
      expect(stats).toHaveProperty('totalEntries');
      expect(stats).toHaveProperty('hitRate');
      expect(stats).toHaveProperty('cacheSize');
    });

    it('should clear cache successfully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'cache_management',
          arguments: {
            action: 'clear',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('cleared successfully');
    });

    it('should cleanup cache successfully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'cache_management',
          arguments: {
            action: 'cleanup',
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('cleanup completed');
    });
  });

  describe('tool listing', () => {
    it('should list all available tools', async () => {
      const request = {
        method: 'tools/list',
        params: {},
      };

      const response = await (server as any).server.request(request);
      
      expect(response.tools).toBeDefined();
      expect(response.tools.length).toBe(5);
      
      const toolNames = response.tools.map((tool: any) => tool.name);
      expect(toolNames).toContain('fetch_content');
      expect(toolNames).toContain('batch_fetch_content');
      expect(toolNames).toContain('manage_proxy');
      expect(toolNames).toContain('manage_auth');
      expect(toolNames).toContain('cache_management');
    });

    it('should provide correct tool schemas', async () => {
      const request = {
        method: 'tools/list',
        params: {},
      };

      const response = await (server as any).server.request(request);
      
      const fetchTool = response.tools.find((tool: any) => tool.name === 'fetch_content');
      expect(fetchTool.inputSchema.properties.url).toBeDefined();
      expect(fetchTool.inputSchema.properties.options).toBeDefined();
      expect(fetchTool.inputSchema.required).toContain('url');
    });
  });

  describe('error handling', () => {
    it('should handle unknown tool calls', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'unknown_tool',
          arguments: {},
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
      expect(response.content[0].text).toContain('Unknown tool');
    });

    it('should handle malformed requests gracefully', async () => {
      const request = {
        method: 'tools/call',
        params: {
          name: 'fetch_content',
          arguments: {
            // Missing required url parameter
          },
        },
      };

      const response = await (server as any).server.request(request);
      
      expect(response.content[0].text).toContain('Error:');
    });
  });
});
