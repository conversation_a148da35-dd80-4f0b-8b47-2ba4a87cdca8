import { ServerConfig } from '../../src/types/index.js';
import { randomBytes } from 'crypto';
import { mkdirSync, rmSync, existsSync } from 'fs';
import { join } from 'path';

// 测试配置生成器
export function createTestConfig(overrides: Partial<ServerConfig> = {}): ServerConfig {
  const testDataDir = join(process.cwd(), 'test-data');
  
  // 确保测试数据目录存在
  if (!existsSync(testDataDir)) {
    mkdirSync(testDataDir, { recursive: true });
  }

  const baseConfig: ServerConfig = {
    server: {
      port: 3001,
      host: 'localhost',
      maxConcurrency: 5,
      timeout: 10000,
    },
    proxy: {
      autoDetect: false,
      customProxies: [],
      healthCheckInterval: 60000,
      rotationStrategy: 'round-robin',
    },
    cache: {
      enabled: false, // 测试中默认禁用缓存
      maxSize: 100,
      defaultTTL: 300,
      cleanupInterval: 60000,
      dbPath: join(testDataDir, 'test-cache.db'),
    },
    fetcher: {
      userAgents: ['Test-Agent/1.0'],
      defaultHeaders: {
        'Accept': 'text/html',
      },
      retryConfig: {
        maxRetries: 1,
        baseDelay: 100,
        maxDelay: 1000,
        backoffFactor: 2,
      },
      rateLimiting: {
        enabled: false,
        requestsPerSecond: 100,
        burstSize: 200,
      },
    },
    security: {
      encryptionKey: randomBytes(32).toString('hex'),
      authStorePath: join(testDataDir, 'test-auth.db'),
      logLevel: 'error',
      maxRequestSize: 1048576,
    },
  };

  return {
    ...baseConfig,
    ...overrides,
    server: { ...baseConfig.server, ...overrides.server },
    proxy: { ...baseConfig.proxy, ...overrides.proxy },
    cache: { ...baseConfig.cache, ...overrides.cache },
    fetcher: { ...baseConfig.fetcher, ...overrides.fetcher },
    security: { ...baseConfig.security, ...overrides.security },
  };
}

// 清理测试数据
export function cleanupTestData(): void {
  const testDataDir = join(process.cwd(), 'test-data');
  if (existsSync(testDataDir)) {
    rmSync(testDataDir, { recursive: true, force: true });
  }
}

// 模拟HTML内容
export const mockHtmlContent = `
<!DOCTYPE html>
<html>
<head>
  <title>Test Article</title>
  <meta name="author" content="Test Author">
  <meta name="description" content="This is a test article">
</head>
<body>
  <article>
    <h1>Test Article Title</h1>
    <p class="byline">By Test Author</p>
    <div class="content">
      <p>This is the first paragraph of the test article.</p>
      <p>This is the second paragraph with some <a href="https://example.com">links</a>.</p>
      <img src="https://example.com/image.jpg" alt="Test Image">
      <ul>
        <li>List item 1</li>
        <li>List item 2</li>
      </ul>
    </div>
  </article>
</body>
</html>
`;

// 模拟简单HTML内容
export const mockSimpleHtml = `
<html>
<body>
  <h1>Simple Title</h1>
  <p>Simple content paragraph.</p>
</body>
</html>
`;

// 模拟复杂HTML内容
export const mockComplexHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Complex Article</title>
  <meta property="og:title" content="Complex Article">
  <meta property="og:description" content="A complex article with multiple sections">
  <meta property="article:author" content="Complex Author">
  <meta property="article:published_time" content="2024-01-01T00:00:00Z">
</head>
<body>
  <header>
    <nav>Navigation</nav>
  </header>
  <main>
    <article>
      <h1>Complex Article Title</h1>
      <div class="meta">
        <span class="author">Complex Author</span>
        <time datetime="2024-01-01">January 1, 2024</time>
      </div>
      <div class="content">
        <h2>Section 1</h2>
        <p>First section content with <strong>bold text</strong> and <em>italic text</em>.</p>
        
        <h2>Section 2</h2>
        <p>Second section with a <a href="https://example.com">link</a>.</p>
        
        <blockquote>
          <p>This is a quote from someone important.</p>
        </blockquote>
        
        <h3>Subsection</h3>
        <ul>
          <li>Item 1</li>
          <li>Item 2 with <code>inline code</code></li>
          <li>Item 3</li>
        </ul>
        
        <pre><code>
function example() {
  return "Hello, World!";
}
        </code></pre>
        
        <table>
          <thead>
            <tr>
              <th>Column 1</th>
              <th>Column 2</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Data 1</td>
              <td>Data 2</td>
            </tr>
          </tbody>
        </table>
        
        <img src="https://example.com/image1.jpg" alt="Image 1">
        <img src="https://example.com/image2.png" alt="Image 2">
      </div>
    </article>
  </main>
  <footer>
    <p>Footer content</p>
  </footer>
</body>
</html>
`;

// 延迟函数
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 创建模拟的HTTP响应
export function createMockResponse(data: string, status = 200, headers = {}) {
  return {
    data,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: {
      'content-type': 'text/html',
      ...headers,
    },
    config: {},
    request: {},
  };
}

// 错误生成器
export class TestError extends Error {
  constructor(message: string, public code?: string, public statusCode?: number) {
    super(message);
    this.name = 'TestError';
  }
}

// 随机字符串生成器
export function randomString(length = 10): string {
  return randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
}

// URL验证器
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 等待条件满足
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout = 5000,
  interval = 100
): Promise<void> {
  const start = Date.now();
  
  while (Date.now() - start < timeout) {
    if (await condition()) {
      return;
    }
    await delay(interval);
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
}

// 测试数据生成器
export const testData = {
  urls: {
    valid: [
      'https://example.com',
      'https://test.example.com/path',
      'http://localhost:3000',
    ],
    invalid: [
      'not-a-url',
      'ftp://example.com',
      'javascript:alert(1)',
      '',
    ],
  },
  
  proxies: [
    {
      id: 'test-proxy-1',
      type: 'http' as const,
      host: 'proxy1.example.com',
      port: 8080,
      enabled: true,
      priority: 1,
      isHealthy: true,
    },
    {
      id: 'test-proxy-2',
      type: 'socks5' as const,
      host: 'proxy2.example.com',
      port: 1080,
      username: 'user',
      password: 'pass',
      enabled: true,
      priority: 2,
      isHealthy: false,
    },
  ],
  
  authConfigs: [
    {
      type: 'basic' as const,
      credentials: {
        username: 'testuser',
        password: 'testpass',
      },
      domain: 'example.com',
    },
    {
      type: 'bearer' as const,
      credentials: {
        token: 'test-bearer-token',
      },
      domain: 'api.example.com',
    },
  ],
};
