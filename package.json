{"name": "@our-aicorp/mcp-content-hunter", "version": "1.0.0", "description": "MCP service for AI assistants to extract and structure web content", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "bin": {"mcp-content-hunter": "dist/index.js"}, "files": ["dist/**/*", "examples/**/*", "README.md", "LICENSE", "CHANGELOG.md"], "repository": {"type": "git", "url": "https://github.com/our-aicorp/mcp-content-hunter.git"}, "homepage": "https://github.com/our-aicorp/mcp-content-hunter#readme", "bugs": {"url": "https://github.com/our-aicorp/mcp-content-hunter/issues"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "mcp": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build", "preversion": "npm run lint && npm run test", "version": "npm run build && git add -A dist", "postversion": "git push && git push --tags", "release": "./scripts/release.sh", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish", "setup:dev": "./scripts/setup-dev.sh"}, "keywords": ["mcp", "model-context-protocol", "content-extraction", "web-scraping", "ai-assistant", "typescript", "claude", "cursor", "cline", "augment", "readability", "playwright"], "author": {"name": "Our AI Corp", "email": "<EMAIL>", "url": "https://github.com/our-aicorp"}, "license": "MIT", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@mozilla/readability": "^0.5.0", "axios": "^1.6.0", "better-sqlite3": "^9.2.0", "jsdom": "^23.0.0", "playwright": "^1.40.0", "turndown": "^7.1.0", "zod": "^3.22.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.0", "@types/jest": "^29.5.0", "@types/jsdom": "^21.1.0", "@types/node": "^20.0.0", "@types/turndown": "^5.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}