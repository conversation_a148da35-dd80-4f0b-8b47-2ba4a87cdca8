# MCP Content Hunter

A Model Context Protocol (MCP) server for intelligent web content extraction and structuring, providing high-quality, structured web content for AI assistants.

## ✨ Features

- **Smart Content Extraction**: Static and dynamic web page content extraction
- **Structured Output**: Automatic conversion to Markdown and JSON formats
- **Proxy Support**: Auto-detect system proxy, custom proxy pool management
- **Authentication**: Multiple auth methods (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, OAuth2, etc.)
- **Intelligent Caching**: SQLite-based multi-level caching system
- **Batch Processing**: Concurrent processing of multiple URLs
- **Retry Logic**: Smart retry mechanism with exponential backoff
- **Modern Architecture**: TypeScript + MCP protocol
- **Lightweight**: Minimal dependencies, high performance
- **Robust**: Comprehensive error handling and recovery

## 📦 Installation

### Prerequisites
- Node.js >= 18.0.0
- npm >= 8.0.0

### Install from NPM
```bash
npm install -g @our-aicorp/mcp-content-hunter
```

### Development Setup
```bash
git clone https://github.com/our-aicorp/mcp-content-hunter.git
cd mcp-content-hunter
npm install
npm run build
```

## ⚙️ AI Tool Integration

### Claude Desktop
Add to your Claude Desktop config file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "content-hunter": {
      "command": "npx",
      "args": ["-y", "@our-aicorp/mcp-content-hunter"],
      "env": {
        "MCP_LOG_LEVEL": "info"
      }
    }
  }
}
```

### Cursor
1. Open Cursor settings (Cmd/Ctrl + ,)
2. Search for "MCP"
3. Add the server configuration:

```json
{
  "mcpServers": {
    "content-hunter": {
      "command": "npx",
      "args": ["-y", "@our-aicorp/mcp-content-hunter"],
      "env": {
        "MCP_LOG_LEVEL": "info"
      }
    }
  }
}
```

### Other AI Tools
Similar configuration can be used for Cline, Augment, and other MCP-compatible tools.

## 🚀 Usage

Once configured in your AI tool, you can use the following commands:

### Single URL Content Extraction
```
Please extract content from this webpage: https://example.com/article
```

### Batch Content Extraction
```
Please extract content from these URLs:
- https://example.com/article1
- https://example.com/article2
- https://example.com/article3
```

### Dynamic Content (SPA)
```
Please extract content from this SPA website using dynamic rendering: https://spa-app.com/page
```

## 🏗️ Architecture

### Core Components
- **Content Fetcher**: Unified content extraction interface
- **Content Processor**: Content extraction and structuring (Mozilla Readability + Turndown)
- **Proxy Manager**: Proxy configuration and health monitoring
- **Auth Manager**: Authentication configuration management
- **Cache Manager**: Multi-level caching (SQLite + Memory)
- **MCP Server**: Model Context Protocol server implementation

## 🧪 Development

```bash
# Run tests
npm test

# Lint code
npm run lint

# Build project
npm run build
```

## 📊 Performance

- **Single URL**: < 10 seconds (static pages < 3 seconds)
- **Concurrent processing**: 50+ URLs
- **Memory usage**: < 512MB under normal load
- **Cache hit rate**: > 80%

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔧 Development

### Environment Setup
```bash
git clone https://github.com/our-aicorp/mcp-content-hunter.git
cd mcp-content-hunter
npm run setup:dev
```

### Release
```bash
npm run release
```

## 📈 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history.
